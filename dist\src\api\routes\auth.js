"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setDatabase = setDatabase;
const express_1 = require("express");
const bcrypt_1 = __importDefault(require("bcrypt"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const zod_1 = require("zod");
// We'll get the db instance passed in or create it here
let db;
const router = (0, express_1.Router)();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
// Function to set the database instance
function setDatabase(database) {
    db = database;
}
// Validation schemas
const registerSchema = zod_1.z.object({
    screenName: zod_1.z.string().min(3).max(20),
    password: zod_1.z.string().min(6),
    email: zod_1.z.string().email().optional()
});
const loginSchema = zod_1.z.object({
    screenName: zod_1.z.string(),
    password: zod_1.z.string()
});
// Register new player
router.post('/register', async (req, res) => {
    try {
        const { screenName, password, email } = registerSchema.parse(req.body);
        // Check if player already exists
        const existingPlayer = await db.collection('players').findOne({ screenName });
        if (existingPlayer) {
            return res.status(400).json({ error: 'Player name already exists' });
        }
        // Hash password
        const hashedPassword = await bcrypt_1.default.hash(password, 10);
        // Create new player
        const newPlayer = {
            screenName,
            password: hashedPassword,
            email,
            isFullAccount: true,
            hasMinfinity: true,
            isBacker: false,
            rank: 1,
            stat_ItemsPlaced: 0,
            unfindable: false,
            ageDays: 0,
            leftMinfinityAmount: 1000,
            boostsLeft: 10,
            createdAt: new Date(),
            lastLogin: new Date()
        };
        const result = await db.collection('players').insertOne(newPlayer);
        const playerId = result.insertedId.toString();
        // Generate JWT token
        const token = jsonwebtoken_1.default.sign({ playerId, screenName }, JWT_SECRET, { expiresIn: '7d' });
        res.status(201).json({
            success: true,
            token,
            player: {
                rid: playerId,
                screenName,
                isFullAccount: newPlayer.isFullAccount,
                rank: newPlayer.rank
            }
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({ error: 'Invalid input', details: error.errors });
        }
        console.error('Registration error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Login player
router.post('/login', async (req, res) => {
    try {
        const { screenName, password } = loginSchema.parse(req.body);
        // Find player
        const player = await db.collection('players').findOne({ screenName });
        if (!player) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        // Verify password
        const validPassword = await bcrypt_1.default.compare(password, player.password);
        if (!validPassword) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }
        // Update last login
        await db.collection('players').updateOne({ _id: player._id }, { $set: { lastLogin: new Date() } });
        // Generate JWT token
        const token = jsonwebtoken_1.default.sign({ playerId: player._id.toString(), screenName: player.screenName }, JWT_SECRET, { expiresIn: '7d' });
        res.json({
            success: true,
            token,
            player: {
                rid: player._id.toString(),
                screenName: player.screenName,
                isFullAccount: player.isFullAccount,
                rank: player.rank,
                hasMinfinity: player.hasMinfinity,
                isBacker: player.isBacker
            }
        });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({ error: 'Invalid input', details: error.errors });
        }
        console.error('Login error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Verify token endpoint
router.get('/verify', async (req, res) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }
    try {
        const decoded = jsonwebtoken_1.default.verify(token, JWT_SECRET);
        const player = await db.collection('players').findOne({ _id: decoded.playerId });
        if (!player) {
            return res.status(404).json({ error: 'Player not found' });
        }
        res.json({
            valid: true,
            player: {
                rid: player._id.toString(),
                screenName: player.screenName,
                isFullAccount: player.isFullAccount,
                rank: player.rank
            }
        });
    }
    catch (error) {
        res.status(403).json({ error: 'Invalid or expired token' });
    }
});
exports.default = router;
//# sourceMappingURL=auth.js.map