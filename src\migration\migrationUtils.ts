import { Db, ObjectId } from 'mongodb';
import { 
  PlayerDocument, 
  AreaDocument, 
  SectorDocument, 
  CreationDocument,
  initializeDatabase 
} from './mongoSchema';

// Migration utilities for transitioning from IndexedDB to MongoDB

export class MigrationUtils {
  private db: Db;

  constructor(database: Db) {
    this.db = database;
  }

  // Initialize the database with proper schema
  async initializeSchema() {
    await initializeDatabase(this.db);
  }

  // Create a default player for testing/development
  async createDefaultPlayer(): Promise<string> {
    const defaultPlayer: PlayerDocument = {
      rid: new ObjectId().toString(),
      screenName: "explorer123",
      password: "$2b$10$defaulthashedpassword", // This should be properly hashed
      isFullAccount: true,
      hasMinfinity: true,
      isBacker: false,
      rank: 10,
      stat_ItemsPlaced: 0,
      unfindable: false,
      ageDays: 0,
      leftMinfinityAmount: 1000,
      boostsLeft: 100,
      createdAt: new Date(),
      lastLogin: new Date()
    };

    const result = await this.db.collection('players').insertOne(defaultPlayer);
    console.log('Created default player:', result.insertedId);
    return result.insertedId.toString();
  }

  // Create a default area for testing
  async createDefaultArea(ownerId: string): Promise<string> {
    const defaultArea: AreaDocument = {
      aid: new ObjectId().toString(),
      gid: new ObjectId().toString(),
      sub: false,
      arn: "Welcome Area",
      agn: "Default Areas",
      aun: "welcome",
      ard: "A welcoming place for new players",
      acl: { x: 0, y: 0 },
      apr: ownerId,
      axx: false,
      aul: false,
      spe: false,
      ece: false,
      mpv: 1,
      createdAt: new Date()
    };

    const result = await this.db.collection('area-data').insertOne(defaultArea);
    console.log('Created default area:', result.insertedId);
    return result.insertedId.toString();
  }

  // Create default sectors for an area
  async createDefaultSectors(areaId: string) {
    const defaultSectors = [
      { x: 0, y: 0 },
      { x: 1, y: 0 },
      { x: 0, y: 1 },
      { x: 1, y: 1 }
    ];

    for (const { x, y } of defaultSectors) {
      const sector: SectorDocument = {
        areaId,
        x,
        y,
        v: 1919,
        iix: [],
        ps: [],
        i: {
          b: [],
          p: [],
          n: [],
          dr: []
        },
        createdAt: new Date()
      };

      await this.db.collection('area-sectors').insertOne(sector);
    }

    console.log(`Created ${defaultSectors.length} default sectors for area ${areaId}`);
  }

  // Migrate player data from IndexedDB format
  async migratePlayerData(idbPlayerData: any): Promise<string> {
    const playerDoc: PlayerDocument = {
      rid: idbPlayerData.rid || new ObjectId().toString(),
      screenName: idbPlayerData.screenName || "Unknown Player",
      password: "$2b$10$defaulthashedpassword", // Default password - should be changed
      isFullAccount: idbPlayerData.isFullAccount ?? true,
      hasMinfinity: idbPlayerData.hasMinfinity ?? true,
      isBacker: idbPlayerData.isBacker ?? false,
      rank: idbPlayerData.rank || 1,
      stat_ItemsPlaced: idbPlayerData.stat_ItemsPlaced || 0,
      unfindable: idbPlayerData.unfindable ?? false,
      ageDays: idbPlayerData.ageDays || 0,
      leftMinfinityAmount: idbPlayerData.leftMinfinityAmount || 1000,
      boostsLeft: idbPlayerData.boostsLeft || 100,
      profileItemIds: idbPlayerData.profileItemIds,
      profileColor: idbPlayerData.profileColor,
      profileBackId: idbPlayerData.profileBackId,
      profileDynaId: idbPlayerData.profileDynaId,
      createdAt: new Date(),
      lastLogin: new Date()
    };

    const result = await this.db.collection('players').insertOne(playerDoc);
    console.log('Migrated player data:', result.insertedId);
    return result.insertedId.toString();
  }

  // Migrate area data from IndexedDB format
  async migrateAreaData(idbAreaData: any): Promise<string> {
    const areaDoc: AreaDocument = {
      aid: idbAreaData.aid || new ObjectId().toString(),
      gid: idbAreaData.gid || new ObjectId().toString(),
      sub: idbAreaData.sub ?? false,
      arn: idbAreaData.arn || "Unnamed Area",
      agn: idbAreaData.agn || "Default Group",
      aun: idbAreaData.aun || "unnamed",
      ard: idbAreaData.ard || "No description",
      acl: idbAreaData.acl || { x: 0, y: 0 },
      iid: idbAreaData.iid,
      adr: idbAreaData.adr,
      apr: idbAreaData.apr || "000000000000000000000000",
      axx: idbAreaData.axx ?? false,
      aul: idbAreaData.aul ?? false,
      spe: idbAreaData.spe ?? false,
      ece: idbAreaData.ece ?? false,
      mpv: idbAreaData.mpv || 1,
      createdAt: new Date()
    };

    const result = await this.db.collection('area-data').insertOne(areaDoc);
    console.log('Migrated area data:', result.insertedId);
    return result.insertedId.toString();
  }

  // Migrate sector data from IndexedDB format
  async migrateSectorData(idbSectorData: any, areaId: string): Promise<string> {
    const sectorDoc: SectorDocument = {
      areaId,
      x: idbSectorData.x || 0,
      y: idbSectorData.y || 0,
      v: idbSectorData.v || 1919,
      iix: idbSectorData.iix || [],
      ps: idbSectorData.ps || [],
      i: idbSectorData.i || { b: [], p: [], n: [], dr: [] },
      createdAt: new Date()
    };

    const result = await this.db.collection('area-sectors').insertOne(sectorDoc);
    console.log('Migrated sector data:', result.insertedId);
    return result.insertedId.toString();
  }

  // Migrate creation data from IndexedDB format
  async migrateCreationData(idbCreationData: any): Promise<string> {
    const creationDoc: CreationDocument = {
      name: idbCreationData.name || "Unnamed Creation",
      base: idbCreationData.base || "",
      creator: idbCreationData.creator || "000000000000000000000000",
      props: idbCreationData.props,
      direction: idbCreationData.direction,
      likes: idbCreationData.likes || 0,
      collections: idbCreationData.collections || 0,
      createdAt: new Date()
    };

    const result = await this.db.collection('creations').insertOne(creationDoc);
    console.log('Migrated creation data:', result.insertedId);
    return result.insertedId.toString();
  }

  // Batch migration for multiple items
  async batchMigrateData(dataType: string, items: any[]): Promise<string[]> {
    const results: string[] = [];
    
    for (const item of items) {
      try {
        let result: string;
        
        switch (dataType) {
          case 'players':
            result = await this.migratePlayerData(item);
            break;
          case 'areas':
            result = await this.migrateAreaData(item);
            break;
          case 'creations':
            result = await this.migrateCreationData(item);
            break;
          default:
            throw new Error(`Unknown data type: ${dataType}`);
        }
        
        results.push(result);
      } catch (error) {
        console.error(`Error migrating ${dataType} item:`, error);
      }
    }
    
    console.log(`Batch migrated ${results.length} ${dataType} items`);
    return results;
  }

  // Validate migration integrity
  async validateMigration(): Promise<boolean> {
    try {
      const collections = [
        'players',
        'area-data',
        'area-sectors',
        'creations'
      ];

      for (const collectionName of collections) {
        const count = await this.db.collection(collectionName).countDocuments();
        console.log(`${collectionName}: ${count} documents`);
      }

      // Check for required indexes
      const playerIndexes = await this.db.collection('players').indexes();
      const areaIndexes = await this.db.collection('area-data').indexes();
      
      console.log('Player indexes:', playerIndexes.length);
      console.log('Area indexes:', areaIndexes.length);

      return true;
    } catch (error) {
      console.error('Migration validation failed:', error);
      return false;
    }
  }

  // Clean up old data (use with caution)
  async cleanupOldData() {
    const collections = [
      'players',
      'area-data',
      'area-sectors',
      'creations',
      'multis',
      'motions-of-body',
      'holders',
      'creation-stats',
      'creation-painter-data'
    ];

    for (const collectionName of collections) {
      await this.db.collection(collectionName).deleteMany({});
      console.log(`Cleaned up collection: ${collectionName}`);
    }
  }

  // Setup development environment with sample data
  async setupDevelopmentData() {
    console.log('Setting up development data...');
    
    // Create default player
    const playerId = await this.createDefaultPlayer();
    
    // Create default area
    const areaId = await this.createDefaultArea(playerId);
    
    // Create default sectors
    await this.createDefaultSectors(areaId);
    
    console.log('Development data setup complete');
    return { playerId, areaId };
  }
}
