{"root": ["./_code/service-worker/dataimport_area.ts", "./_code/service-worker/dataimport_profile.ts", "./_code/service-worker/playerdatamanager.ts", "./_code/service-worker/storage.ts", "./_code/service-worker/index.ts", "./_code/service-worker/localcreations.ts", "./_code/service-worker/localminimap.ts", "./_code/service-worker/api/api.ts", "./_code/service-worker/boilerplate/basicrouter.ts", "./_code/service-worker/boilerplate/cache.ts", "./_code/service-worker/boilerplate/mongoid.ts", "./_code/service-worker/boilerplate/ws.ts", "./globals.d.ts", "./service-worker.ts"], "version": "5.6.3"}