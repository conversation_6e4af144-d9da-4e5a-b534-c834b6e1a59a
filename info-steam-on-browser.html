<!DOCTYPE html>
<html lang="en">
    <head>
        <title>Offlineland</title>
        <link rel="icon" type="image/png" href="/static/offlineland/favicons/unplugged-1-grey.png">
        <link href="/static/offlineland/style.css" rel="stylesheet" />
        <link href="/_code/libs/toastify.css" rel="stylesheet" />
    </head>
    <body>
        <nav class="navbar">
            <div class="navbar-start">

                <a href="/" class="btn btn-ghost gap-1 h-2 min-h-[2rem] px-2 mx-1 ">
                    <img style="image-rendering:pixelated" src="/static/offlineland/favicons/unplugged-1-grey.png" class="w-6 h-6" />
                    <span class="font-title text-base-content text-lg md:text-xl">offlineland.io</span>
                </a>
            </div>

            <div class="navbar-center">
                <!-- todo: move that to navbar-end, use a rightside button, figure out how to not have the menu go beyond the screen-->
                <div class="dropdown">
                    <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16" />
                        </svg>

                        <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                            <li>
                                <details class="">
                                    <summary class="py-1">
                                        Museum
                                    </summary>
                                    <ul class="p-2 bg-base-100 rounded-t-none">
                                        <li class="disabled"><a>
                                            Maps
                                            <span class="badge badge-sm badge-primary">Soon!</span>
                                        </a></li>
                                        <li class="disabled"><a>
                                            Sounds
                                            <span class="badge badge-sm badge-primary">Soon!</span>
                                        </a></li>
                                        <li class="disabled"><a>
                                            Universe Search
                                            <span class="badge badge-sm badge-primary">Soon!</span>
                                        </a></li>
                                    </ul>
                                </details>
                            </li>
                            <li class="disabled"><a class="py-1">
                                Achievements
                                <span class="badge badge-sm badge-primary">Soon!</span>
                            </a></li>
                            <li><a class="py-1" href="/exporter">Exporter</a></li>
                            <li><a class="py-1" href="https://online.offlineland.io">Onlineland</a></li>
                        </ul>
                    </div>
                </div>
                

            </div>

            <div class="navbar-end hidden lg:flex">
                <ul class="menu menu-horizontal px-1">
                    <li>
                        <details class="">
                            <summary class="py-1">
                                Museum
                            </summary>
                            <ul class="p-2 bg-base-100 rounded-t-none">
                                <li class="disabled"><a>
                                    Maps
                                    <span class="badge badge-sm badge-primary">Soon!</span>
                                </a></li>
                                <li class="disabled"><a>
                                    Sounds
                                    <span class="badge badge-sm badge-primary">Soon!</span>
                                </a></li>
                                <li class="disabled"><a>
                                    Universe Search
                                    <span class="badge badge-sm badge-primary">Soon!</span>
                                </a></li>
                            </ul>
                        </details>
                    </li>
                    <li class="disabled"><a class="py-1">
                        Achievements
                        <span class="badge badge-sm badge-primary">Soon!</span>
                    </a></li>
                    <li><a class="py-1" href="/exporter">Exporter</a></li>
                    <li><a class="py-1" href="https://online.offlineland.io">Onlineland</a></li>
                </ul>
            </div>
        </nav>


        <main class="m-3 md:m-10">
            <div class="text-center pb-6">
                <h1 class="text-2xl font-bold">How to use your steam account on browser</h1>
            </div>


            <div class="">
                <div class="flex flex-col items-center gap-16 md:gap-8">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-8">
                        <div class="flex flex-col items-center justify-center space-y-6">
                            <p class="max-w-md">
                                Open the Dev Tools on Steam by pressing <span><kbd class="kbd kbd-sm">Ctrl</kbd> + <kbd class="kbd kbd-sm">F12</kbd></span>.

                            </p>
                            <p class="max-w-md">
                                Click on the (1) Resource tab, then (2) Cookies, and copy the value of the <kbd class="kbd kbd-sm">s</kbd> cookie (double-click on the value, then ctrl+c or drag it to notepad.)
                            </p>
                            <p class="w-full max-w-md">
                                The <kbd class="kbd kbd-sm">s</kbd> cookie starts with <kbd class="kbd kbd-sm">s%3A</kbd>.
                            </p>
                        </div>

                        <div>
                            <img src="/static/offlineland/steam-devtools.png" class="max-h-fit rounded-xl">
                        </div>
                    </div>


                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-8">
                        <div class="flex flex-col items-center justify-center space-y-6">
                            <p class="max-w-md">
                                On your browser, go to Manyland.com, then open the devtools again (<kbd class="kbd kbd-sm">F12</kbd>).
                            </p>
                            <p class="max-w-md">
                                Click on the (1) Storage tab, then (2) Cookies.
                                This time, replace the value of the <kbd class="kbd kbd-sm">s</kbd> with the one you copied from Steam.
                            </p>
                            <p class="max-w-md">
                                Refresh. If everything goes well, you'll be logged-in on your steam account! You can now continue with the <a href="/exporter#instructions" class="link link-secondary">exporter</a>.
                            </p>

                            <p class="w-full max-w-md">
                                If it didn't work, try going over the steps again.
                            </p>

                            <div role="alert" class="alert alert-warning max-w-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                                <span>
                                    Note: do not share this cookie with anyone! If you do, they'll be able to do exactly as above and log into your account.
                                </span>
                            </div>
                        </div>

                        <div>
                            <img src="/static/offlineland/browser-devtools.png" class="max-h-fit rounded-xl">
                        </div>
                    </div>

                </div>
            </div>



        </main>

        <footer class="footer footer-center p-4 bg-neutral text-neutral-content">
            <div class="text-xs opacity-80">
                <p>offlineland.io is not affiliated with manyland.com or its developers.</p>
                <p>If you need help, please send us a mail at <a class="link" href="mailto:<EMAIL>"><EMAIL></a>!</p>
            </div>
        </footer>
    </body>
</html>
