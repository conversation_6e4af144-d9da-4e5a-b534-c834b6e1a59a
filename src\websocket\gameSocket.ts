import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';
import jwt from 'jsonwebtoken';
import { ObjectId, Db } from 'mongodb';

// We'll get the db instance passed in
let db: Db;

// Function to set the database instance
export function setDatabase(database: Db) {
  db = database;
}

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

interface AuthenticatedWebSocket extends WebSocket {
  playerId?: string;
  playerName?: string;
  currentArea?: string;
}

interface GameMessage {
  m: string;
  data: any;
}

interface PlayerPosition {
  x: number;
  y: number;
  areaId: string;
}

class GameWebSocketServer {
  private wss: WebSocketServer;
  private clients: Map<string, AuthenticatedWebSocket> = new Map();
  private areaClients: Map<string, Set<string>> = new Map();

  constructor(server: Server) {
    this.wss = new WebSocketServer({
      server,
      path: '/ws'
    });

    this.wss.on('connection', this.handleConnection.bind(this));
  }

  private async handleConnection(ws: AuthenticatedWebSocket, request: any) {
    console.log('New WebSocket connection');

    // Handle authentication
    ws.on('message', async (data: Buffer) => {
      try {
        const message: GameMessage = JSON.parse(data.toString());

        if (message.m === 'AUTH') {
          await this.authenticateClient(ws, message.data.token);
        } else if (ws.playerId) {
          await this.handleGameMessage(ws, message);
        } else {
          ws.close(1008, 'Authentication required');
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
        ws.close(1011, 'Internal server error');
      }
    });

    ws.on('close', () => {
      this.handleDisconnection(ws);
    });

    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
    });
  }

  private async authenticateClient(ws: AuthenticatedWebSocket, token: string) {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as any;
      const player = await db.collection('players').findOne({
        _id: new ObjectId(decoded.playerId)
      });

      if (!player) {
        ws.close(1008, 'Invalid player');
        return;
      }

      ws.playerId = player._id.toString();
      ws.playerName = player.screenName;

      this.clients.set(ws.playerId, ws);

      // Send authentication success
      this.sendMessage(ws, {
        m: 'AUTH_SUCCESS',
        data: {
          playerId: ws.playerId,
          playerName: ws.playerName
        }
      });

      console.log(`Player ${ws.playerName} authenticated via WebSocket`);

    } catch (error) {
      console.error('WebSocket authentication error:', error);
      ws.close(1008, 'Authentication failed');
    }
  }

  private async handleGameMessage(ws: AuthenticatedWebSocket, message: GameMessage) {
    switch (message.m) {
      case 'JOIN_AREA':
        await this.handleJoinArea(ws, message.data);
        break;

      case 'LEAVE_AREA':
        await this.handleLeaveArea(ws, message.data);
        break;

      case 'PLAYER_MOVE':
        await this.handlePlayerMove(ws, message.data);
        break;

      case 'PLACE_ITEM':
        await this.handlePlaceItem(ws, message.data);
        break;

      case 'CHAT_MESSAGE':
        await this.handleChatMessage(ws, message.data);
        break;

      case 'SYNC_REQUEST':
        await this.handleSyncRequest(ws, message.data);
        break;

      default:
        console.log('Unknown message type:', message.m);
    }
  }

  private async handleJoinArea(ws: AuthenticatedWebSocket, data: { areaId: string }) {
    const { areaId } = data;

    // Leave previous area if any
    if (ws.currentArea) {
      await this.handleLeaveArea(ws, { areaId: ws.currentArea });
    }

    ws.currentArea = areaId;

    // Add to area clients
    if (!this.areaClients.has(areaId)) {
      this.areaClients.set(areaId, new Set());
    }
    this.areaClients.get(areaId)!.add(ws.playerId!);

    // Notify other players in the area
    this.broadcastToArea(areaId, {
      m: 'PLAYER_JOINED',
      data: {
        playerId: ws.playerId,
        playerName: ws.playerName
      }
    }, ws.playerId);

    // Send current area state to the joining player
    const areaPlayers = Array.from(this.areaClients.get(areaId) || [])
      .filter(id => id !== ws.playerId)
      .map(id => {
        const client = this.clients.get(id);
        return client ? {
          playerId: id,
          playerName: client.playerName
        } : null;
      })
      .filter(Boolean);

    this.sendMessage(ws, {
      m: 'AREA_STATE',
      data: {
        areaId,
        players: areaPlayers
      }
    });
  }

  private async handleLeaveArea(ws: AuthenticatedWebSocket, data: { areaId: string }) {
    const { areaId } = data;

    if (this.areaClients.has(areaId)) {
      this.areaClients.get(areaId)!.delete(ws.playerId!);

      // Clean up empty area
      if (this.areaClients.get(areaId)!.size === 0) {
        this.areaClients.delete(areaId);
      }
    }

    // Notify other players
    this.broadcastToArea(areaId, {
      m: 'PLAYER_LEFT',
      data: {
        playerId: ws.playerId,
        playerName: ws.playerName
      }
    }, ws.playerId);

    ws.currentArea = undefined;
  }

  private async handlePlayerMove(ws: AuthenticatedWebSocket, data: PlayerPosition) {
    if (!ws.currentArea) return;

    // Update player position in database
    await db.collection('player-positions').updateOne(
      { playerId: ws.playerId },
      {
        $set: {
          ...data,
          updatedAt: new Date()
        }
      },
      { upsert: true }
    );

    // Broadcast to other players in the area
    this.broadcastToArea(ws.currentArea, {
      m: 'PLAYER_MOVED',
      data: {
        playerId: ws.playerId,
        ...data
      }
    }, ws.playerId);
  }

  private async handlePlaceItem(ws: AuthenticatedWebSocket, data: any) {
    if (!ws.currentArea) return;

    // Validate and save item placement
    // This would integrate with your existing placement logic

    // Broadcast to other players in the area
    this.broadcastToArea(ws.currentArea, {
      m: 'ITEM_PLACED',
      data: {
        playerId: ws.playerId,
        ...data
      }
    }, ws.playerId);
  }

  private async handleChatMessage(ws: AuthenticatedWebSocket, data: { message: string }) {
    if (!ws.currentArea) return;

    const chatMessage = {
      playerId: ws.playerId,
      playerName: ws.playerName,
      message: data.message,
      timestamp: new Date()
    };

    // Save chat message to database
    await db.collection('chat-messages').insertOne({
      ...chatMessage,
      areaId: ws.currentArea
    });

    // Broadcast to other players in the area
    this.broadcastToArea(ws.currentArea, {
      m: 'CHAT_MESSAGE',
      data: chatMessage
    });
  }

  private async handleSyncRequest(ws: AuthenticatedWebSocket, data: any) {
    // Handle synchronization requests for game state
    // This would be used for things like moving blocks, etc.

    this.sendMessage(ws, {
      m: 'SYNC_RESPONSE',
      data: {
        // Sync data would go here
      }
    });
  }

  private handleDisconnection(ws: AuthenticatedWebSocket) {
    if (ws.playerId) {
      console.log(`Player ${ws.playerName} disconnected`);

      // Remove from area
      if (ws.currentArea) {
        this.handleLeaveArea(ws, { areaId: ws.currentArea });
      }

      // Remove from clients
      this.clients.delete(ws.playerId);
    }
  }

  private sendMessage(ws: AuthenticatedWebSocket, message: GameMessage) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  private broadcastToArea(areaId: string, message: GameMessage, excludePlayerId?: string) {
    const areaPlayerIds = this.areaClients.get(areaId);
    if (!areaPlayerIds) return;

    areaPlayerIds.forEach(playerId => {
      if (playerId !== excludePlayerId) {
        const client = this.clients.get(playerId);
        if (client) {
          this.sendMessage(client, message);
        }
      }
    });
  }

  public getConnectedPlayers(): number {
    return this.clients.size;
  }

  public getAreaPlayerCount(areaId: string): number {
    return this.areaClients.get(areaId)?.size || 0;
  }
}

export default GameWebSocketServer;
