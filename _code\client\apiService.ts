// Client-side API service to replace service worker calls
// This will handle all communication with the real backend API

interface ApiResponse<T = any> {
  success?: boolean;
  data?: T;
  error?: string;
  details?: any;
}

class ApiService {
  private baseUrl: string;
  private token: string | null = null;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
    this.loadToken();
  }

  // Token management
  private loadToken() {
    this.token = localStorage.getItem('auth_token');
  }

  private saveToken(token: string) {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  private clearToken() {
    this.token = null;
    localStorage.removeItem('auth_token');
  }

  // HTTP request helper
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}/api${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: data.error || 'Request failed',
          details: data.details
        };
      }

      return {
        success: true,
        data
      };
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: 'Network error'
      };
    }
  }

  // Authentication methods
  async login(screenName: string, password: string): Promise<ApiResponse> {
    const response = await this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ screenName, password })
    });

    if (response.success && response.data?.token) {
      this.saveToken(response.data.token);
    }

    return response;
  }

  async register(screenName: string, password: string, email?: string): Promise<ApiResponse> {
    const response = await this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ screenName, password, email })
    });

    if (response.success && response.data?.token) {
      this.saveToken(response.data.token);
    }

    return response;
  }

  async logout() {
    this.clearToken();
  }

  async verifyToken(): Promise<ApiResponse> {
    return this.request('/auth/verify');
  }

  // Player methods
  async getProfile(): Promise<ApiResponse> {
    return this.request('/player/profile');
  }

  async updateProfile(updates: any): Promise<ApiResponse> {
    return this.request('/player/profile', {
      method: 'PUT',
      body: JSON.stringify(updates)
    });
  }

  async getSettings(): Promise<ApiResponse> {
    return this.request('/player/settings');
  }

  async updateSettings(settings: any): Promise<ApiResponse> {
    return this.request('/player/settings', {
      method: 'PUT',
      body: JSON.stringify(settings)
    });
  }

  async getInventory(): Promise<ApiResponse> {
    return this.request('/player/inventory');
  }

  async getAchievements(): Promise<ApiResponse> {
    return this.request('/player/achievements');
  }

  // Game API methods (matching original /j/ endpoints)
  async initArea(urlName: string): Promise<ApiResponse> {
    return this.request('/j/i/', {
      method: 'POST',
      body: JSON.stringify({ urlName })
    });
  }

  async getCreationDef(creationId: string): Promise<ApiResponse> {
    return this.request(`/j/i/def/${creationId}`);
  }

  async createItem(itemData: any): Promise<ApiResponse> {
    return this.request('/j/i/c/', {
      method: 'POST',
      body: JSON.stringify({ itemData })
    });
  }

  async getMapSectors(sectors: [number, number][], areaId: string, pane: number = 0): Promise<ApiResponse> {
    return this.request('/j/m/s/', {
      method: 'POST',
      body: JSON.stringify({
        s: JSON.stringify(sectors),
        a: areaId,
        p: pane
      })
    });
  }

  async getSectorPlus(x: number, y: number, ap: number, aid: string): Promise<ApiResponse> {
    return this.request(`/j/m/sp/${x}/${y}/${ap}/${aid}`);
  }

  async getFriendsAndBlocked(): Promise<ApiResponse> {
    return this.request('/j/u/fab/');
  }

  async getFreshRank(): Promise<ApiResponse> {
    return this.request('/j/u/gfr/', {
      method: 'POST'
    });
  }

  async unlockAchievement(achievementId: string): Promise<ApiResponse> {
    return this.request('/j/u/a/', {
      method: 'POST',
      body: JSON.stringify({ id: achievementId })
    });
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.token;
  }

  getToken(): string | null {
    return this.token;
  }

  // Error handling helper
  handleApiError(response: ApiResponse, defaultMessage: string = 'An error occurred') {
    if (!response.success) {
      console.error('API Error:', response.error, response.details);
      // You can integrate with your UI notification system here
      return response.error || defaultMessage;
    }
    return null;
  }
}

// Create a singleton instance
const apiService = new ApiService();

// Export for use in other modules
export default apiService;

// Also export the class for testing or multiple instances
export { ApiService };

// Types for better TypeScript support
export interface PlayerProfile {
  rid: string;
  screenName: string;
  isFullAccount: boolean;
  rank: number;
  hasMinfinity: boolean;
  isBacker: boolean;
  stat_ItemsPlaced: number;
  unfindable: boolean;
  ageDays: number;
}

export interface AreaData {
  aid: string;
  gid: string;
  arn: string;
  agn: string;
  aun: string;
  ard: string;
  acl: { x: number; y: number };
  apr: string;
  mpv: number;
}

export interface CreationData {
  _id: string;
  name: string;
  base: string;
  creator: string;
  props?: Record<string, any>;
  direction?: number;
  likes: number;
  collections: number;
  createdAt: string;
}

export interface SectorData {
  _id: string;
  areaId: string;
  x: number;
  y: number;
  v: number;
  iix: any[];
  ps: [number, number][];
  i: {
    b: any[];
    p: any[];
    n: any[];
    dr: any[];
  };
}
