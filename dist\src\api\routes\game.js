"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setDatabase = setDatabase;
const express_1 = require("express");
const zod_1 = require("zod");
const mongodb_1 = require("mongodb");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
// We'll get the db instance passed in
let db;
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
// Function to set the database instance
function setDatabase(database) {
    db = database;
}
// Authentication middleware
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }
    jsonwebtoken_1.default.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
}
const router = (0, express_1.Router)();
// Area initialization - POST /j/i/
router.post('/i/', authenticateToken, async (req, res) => {
    try {
        const schema = zod_1.z.object({
            urlName: zod_1.z.string()
        });
        const { urlName } = schema.parse(req.body);
        // Get or create area data
        let areaData = await db.collection('area-data').findOne({ aun: urlName });
        if (!areaData) {
            // Create default area if it doesn't exist
            areaData = {
                aid: new mongodb_1.ObjectId().toString(),
                gid: new mongodb_1.ObjectId().toString(),
                sub: false,
                arn: urlName,
                agn: urlName,
                aun: urlName,
                ard: `Welcome to ${urlName}`,
                acl: { x: 0, y: 0 },
                apr: "000000000000000000000000", // Default area owner
                axx: false,
                aul: false,
                spe: false,
                ece: false,
                mpv: 1,
                createdAt: new Date()
            };
            await db.collection('area-data').insertOne(areaData);
        }
        // Get player init data
        const player = await db.collection('players').findOne({
            _id: new mongodb_1.ObjectId(req.user.playerId)
        });
        const initData = {
            area: areaData,
            player: {
                rid: player._id.toString(),
                age: Math.floor((Date.now() - player.createdAt.getTime()) / (1000 * 60 * 60 * 24)),
                ifa: player.isFullAccount,
                lma: player.leftMinfinityAmount,
                isb: player.isBacker,
                bbl: player.boostsLeft,
                hmf: player.hasMinfinity,
                stn: {} // Player settings
            }
        };
        res.json(initData);
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({ error: 'Invalid input', details: error.errors });
        }
        console.error('Area init error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get creation definition - GET /j/i/def/:creationId
router.get('/i/def/:creationId', async (req, res) => {
    try {
        const { creationId } = req.params;
        if (!mongodb_1.ObjectId.isValid(creationId)) {
            return res.status(400).json({ error: 'Invalid creation ID' });
        }
        const creation = await db.collection('creations').findOne({
            _id: new mongodb_1.ObjectId(creationId)
        });
        if (!creation) {
            return res.status(404).json({ error: 'Creation not found' });
        }
        res.json(creation);
    }
    catch (error) {
        console.error('Get creation definition error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Create item - POST /j/i/c/
router.post('/i/c/', authenticateToken, async (req, res) => {
    try {
        const schema = zod_1.z.object({
            itemData: zod_1.z.object({
                name: zod_1.z.string(),
                base: zod_1.z.string(),
                props: zod_1.z.record(zod_1.z.any()).optional(),
                direction: zod_1.z.number().optional()
            })
        });
        const { itemData } = schema.parse(req.body);
        const creation = {
            ...itemData,
            creator: req.user.playerId,
            createdAt: new Date(),
            likes: 0,
            collections: 0
        };
        const result = await db.collection('creations').insertOne(creation);
        res.json({ itemId: result.insertedId.toString() });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({ error: 'Invalid input', details: error.errors });
        }
        console.error('Create item error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get map sectors - POST /j/m/s/
router.post('/m/s/', authenticateToken, async (req, res) => {
    try {
        const schema = zod_1.z.object({
            s: zod_1.z.string(), // JSON string of sector coordinates
            a: zod_1.z.string(), // Area ID
            p: zod_1.z.number() // Area pane
        });
        const { s, a, p } = schema.parse(req.body);
        const requestedSectors = JSON.parse(s);
        const sectorData = [];
        for (const [x, y] of requestedSectors) {
            const sector = await db.collection('area-sectors').findOne({
                areaId: a,
                x: x,
                y: y
            });
            if (sector) {
                sectorData.push(sector);
            }
        }
        res.json(sectorData);
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({ error: 'Invalid input', details: error.errors });
        }
        console.error('Get map sectors error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get sector plus (3x3 grid) - GET /j/m/sp/:x/:y/:ap/:aid
router.get('/m/sp/:x/:y/:ap/:aid', async (req, res) => {
    try {
        const { x, y, ap, aid } = req.params;
        const centerX = parseInt(x);
        const centerY = parseInt(y);
        const sectors = [];
        // Get 3x3 grid of sectors around the center
        for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
                const sector = await db.collection('area-sectors').findOne({
                    areaId: aid,
                    x: centerX + dx,
                    y: centerY + dy
                });
                if (sector) {
                    sectors.push(sector);
                }
            }
        }
        res.json(sectors);
    }
    catch (error) {
        console.error('Get sector plus error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Friends and blocked - GET /j/u/fab/
router.get('/u/fab/', authenticateToken, async (req, res) => {
    try {
        const friendsData = await db.collection('player-friends').findOne({
            playerId: req.user.playerId
        });
        res.json({
            friends: friendsData?.friends || [],
            blocked: friendsData?.blocked || []
        });
    }
    catch (error) {
        console.error('Get friends and blocked error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get fresh rank - POST /j/u/gfr/
router.post('/u/gfr/', authenticateToken, async (req, res) => {
    try {
        const player = await db.collection('players').findOne({
            _id: new mongodb_1.ObjectId(req.user.playerId)
        });
        res.json(player?.rank || 1);
    }
    catch (error) {
        console.error('Get fresh rank error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Achievement - POST /j/u/a/
router.post('/u/a/', authenticateToken, async (req, res) => {
    try {
        const schema = zod_1.z.object({
            id: zod_1.z.string()
        });
        const { id } = schema.parse(req.body);
        await db.collection('player-achievements').updateOne({ playerId: req.user.playerId }, {
            $addToSet: {
                achievements: {
                    id,
                    unlockedAt: new Date()
                }
            }
        }, { upsert: true });
        res.json({ success: true });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({ error: 'Invalid input', details: error.errors });
        }
        console.error('Achievement error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
exports.default = router;
//# sourceMappingURL=game.js.map