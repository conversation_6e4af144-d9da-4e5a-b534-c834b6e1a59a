"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabase = initializeDatabase;
// Database initialization and schema setup
async function initializeDatabase(db) {
    console.log('Initializing MongoDB collections and indexes...');
    // Create collections if they don't exist
    const collections = [
        'players',
        'area-data',
        'area-sectors',
        'creations',
        'multis',
        'motions-of-body',
        'holders',
        'creation-stats',
        'creation-painter-data',
        'player-inventory',
        'player-achievements',
        'player-settings',
        'player-friends',
        'player-positions',
        'chat-messages',
        'player-top-creations',
        'minimap-colors',
        'minimap-area-tile-ids'
    ];
    for (const collectionName of collections) {
        try {
            await db.createCollection(collectionName);
            console.log(`Created collection: ${collectionName}`);
        }
        catch (error) {
            if (error.code !== 48) { // Collection already exists
                console.error(`Error creating collection ${collectionName}:`, error);
            }
        }
    }
    // Create indexes for better performance
    await createIndexes(db);
    console.log('Database initialization complete');
}
async function createIndexes(db) {
    try {
        // Player indexes
        await db.collection('players').createIndex({ 'rid': 1 }, { unique: true });
        await db.collection('players').createIndex({ 'screenName': 1 }, { unique: true });
        await db.collection('players').createIndex({ 'email': 1 }, { sparse: true });
        // Area indexes
        await db.collection('area-data').createIndex({ 'aid': 1 }, { unique: true });
        await db.collection('area-data').createIndex({ 'gid': 1 });
        await db.collection('area-data').createIndex({ 'aun': 1 }, { unique: true });
        await db.collection('area-data').createIndex({ 'apr': 1 });
        // Sector indexes
        await db.collection('area-sectors').createIndex({ 'areaId': 1, 'x': 1, 'y': 1 }, { unique: true });
        await db.collection('area-sectors').createIndex({ 'areaId': 1 });
        // Creation indexes
        await db.collection('creations').createIndex({ 'creator': 1 });
        await db.collection('creations').createIndex({ 'createdAt': -1 });
        await db.collection('creations').createIndex({ 'likes': -1 });
        // Multi indexes
        await db.collection('multis').createIndex({ 'creationId': 1 });
        // Motion indexes
        await db.collection('motions-of-body').createIndex({ 'bodyId': 1 }, { unique: true });
        // Holder indexes
        await db.collection('holders').createIndex({ 'holderId': 1 }, { unique: true });
        // Stats indexes
        await db.collection('creation-stats').createIndex({ 'creationId': 1 }, { unique: true });
        // Painter data indexes
        await db.collection('creation-painter-data').createIndex({ 'creationId': 1 }, { unique: true });
        // Player-related indexes
        await db.collection('player-inventory').createIndex({ 'playerId': 1 }, { unique: true });
        await db.collection('player-achievements').createIndex({ 'playerId': 1 }, { unique: true });
        await db.collection('player-settings').createIndex({ 'playerId': 1 }, { unique: true });
        await db.collection('player-friends').createIndex({ 'playerId': 1 }, { unique: true });
        await db.collection('player-positions').createIndex({ 'playerId': 1 }, { unique: true });
        await db.collection('player-positions').createIndex({ 'areaId': 1 });
        // Chat indexes
        await db.collection('chat-messages').createIndex({ 'areaId': 1, 'timestamp': -1 });
        await db.collection('chat-messages').createIndex({ 'playerId': 1 });
        // Top creations indexes
        await db.collection('player-top-creations').createIndex({ 'playerId': 1 }, { unique: true });
        // Minimap indexes
        await db.collection('minimap-colors').createIndex({ 'areaId': 1 });
        await db.collection('minimap-area-tile-ids').createIndex({ 'areaId': 1 });
        console.log('Database indexes created successfully');
    }
    catch (error) {
        console.error('Error creating indexes:', error);
    }
}
//# sourceMappingURL=mongoSchema.js.map