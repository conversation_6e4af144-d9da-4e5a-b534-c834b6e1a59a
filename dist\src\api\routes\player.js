"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const zod_1 = require("zod");
const mongodb_1 = require("mongodb");
const server_1 = require("../../server");
const server_2 = require("../../server");
const router = (0, express_1.Router)();
// Get player profile
router.get('/profile', server_2.authenticateToken, async (req, res) => {
    try {
        const player = await server_1.db.collection('players').findOne({
            _id: new mongodb_1.ObjectId(req.user.playerId)
        });
        if (!player) {
            return res.status(404).json({ error: 'Player not found' });
        }
        // Return profile data (excluding sensitive info like password)
        const { password, ...profileData } = player;
        res.json(profileData);
    }
    catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Update player profile
router.put('/profile', server_2.authenticateToken, async (req, res) => {
    try {
        const updateSchema = zod_1.z.object({
            screenName: zod_1.z.string().min(3).max(20).optional(),
            unfindable: zod_1.z.boolean().optional(),
            profileColor: zod_1.z.string().optional(),
            profileBackId: zod_1.z.string().optional(),
            profileDynaId: zod_1.z.string().optional()
        });
        const updates = updateSchema.parse(req.body);
        // Check if screenName is already taken (if updating screenName)
        if (updates.screenName) {
            const existingPlayer = await server_1.db.collection('players').findOne({
                screenName: updates.screenName,
                _id: { $ne: new mongodb_1.ObjectId(req.user.playerId) }
            });
            if (existingPlayer) {
                return res.status(400).json({ error: 'Screen name already taken' });
            }
        }
        const result = await server_1.db.collection('players').updateOne({ _id: new mongodb_1.ObjectId(req.user.playerId) }, { $set: { ...updates, updatedAt: new Date() } });
        if (result.matchedCount === 0) {
            return res.status(404).json({ error: 'Player not found' });
        }
        res.json({ success: true, message: 'Profile updated successfully' });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({ error: 'Invalid input', details: error.errors });
        }
        console.error('Update profile error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get player settings
router.get('/settings', server_2.authenticateToken, async (req, res) => {
    try {
        const settings = await server_1.db.collection('player-settings').findOne({
            playerId: req.user.playerId
        });
        // Return default settings if none exist
        const defaultSettings = {
            soundEnabled: true,
            musicEnabled: true,
            chatEnabled: true,
            showTutorial: true
        };
        res.json(settings?.settings || defaultSettings);
    }
    catch (error) {
        console.error('Get settings error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Update player settings
router.put('/settings', server_2.authenticateToken, async (req, res) => {
    try {
        const settingsSchema = zod_1.z.object({
            soundEnabled: zod_1.z.boolean().optional(),
            musicEnabled: zod_1.z.boolean().optional(),
            chatEnabled: zod_1.z.boolean().optional(),
            showTutorial: zod_1.z.boolean().optional()
        });
        const settings = settingsSchema.parse(req.body);
        await server_1.db.collection('player-settings').updateOne({ playerId: req.user.playerId }, {
            $set: {
                settings,
                updatedAt: new Date()
            }
        }, { upsert: true });
        res.json({ success: true, message: 'Settings updated successfully' });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({ error: 'Invalid input', details: error.errors });
        }
        console.error('Update settings error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get player inventory
router.get('/inventory', server_2.authenticateToken, async (req, res) => {
    try {
        const inventory = await server_1.db.collection('player-inventory').findOne({
            playerId: req.user.playerId
        });
        res.json(inventory?.items || []);
    }
    catch (error) {
        console.error('Get inventory error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get player achievements
router.get('/achievements', server_2.authenticateToken, async (req, res) => {
    try {
        const achievements = await server_1.db.collection('player-achievements').findOne({
            playerId: req.user.playerId
        });
        res.json(achievements?.achievements || []);
    }
    catch (error) {
        console.error('Get achievements error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Award achievement
router.post('/achievements', server_2.authenticateToken, async (req, res) => {
    try {
        const achievementSchema = zod_1.z.object({
            id: zod_1.z.string(),
            name: zod_1.z.string(),
            description: zod_1.z.string()
        });
        const achievement = achievementSchema.parse(req.body);
        await server_1.db.collection('player-achievements').updateOne({ playerId: req.user.playerId }, {
            $addToSet: {
                achievements: {
                    ...achievement,
                    unlockedAt: new Date()
                }
            }
        }, { upsert: true });
        res.json({ success: true, message: 'Achievement unlocked!' });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({ error: 'Invalid input', details: error.errors });
        }
        console.error('Award achievement error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
exports.default = router;
//# sourceMappingURL=player.js.map