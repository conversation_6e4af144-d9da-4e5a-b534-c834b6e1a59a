// Client-side API service to replace service worker calls
// This will handle all communication with the real backend API
class ApiService {
    constructor(baseUrl = '') {
        this.token = null;
        this.baseUrl = baseUrl;
        this.loadToken();
    }
    // Token management
    loadToken() {
        this.token = localStorage.getItem('auth_token');
    }
    saveToken(token) {
        this.token = token;
        localStorage.setItem('auth_token', token);
    }
    clearToken() {
        this.token = null;
        localStorage.removeItem('auth_token');
    }
    // HTTP request helper
    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}/api${endpoint}`;
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };
        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }
        try {
            const response = await fetch(url, {
                ...options,
                headers
            });
            const data = await response.json();
            if (!response.ok) {
                return {
                    success: false,
                    error: data.error || 'Request failed',
                    details: data.details
                };
            }
            return {
                success: true,
                data
            };
        }
        catch (error) {
            console.error('API request failed:', error);
            return {
                success: false,
                error: 'Network error'
            };
        }
    }
    // Authentication methods
    async login(screenName, password) {
        const response = await this.request('/auth/login', {
            method: 'POST',
            body: JSON.stringify({ screenName, password })
        });
        if (response.success && response.data && response.data.token) {
            this.saveToken(response.data.token);
        }
        return response;
    }
    async register(screenName, password, email) {
        const response = await this.request('/auth/register', {
            method: 'POST',
            body: JSON.stringify({ screenName, password, email })
        });
        if (response.success && response.data && response.data.token) {
            this.saveToken(response.data.token);
        }
        return response;
    }
    async logout() {
        this.clearToken();
    }
    async verifyToken() {
        return this.request('/auth/verify');
    }
    // Player methods
    async getProfile() {
        return this.request('/player/profile');
    }
    async updateProfile(updates) {
        return this.request('/player/profile', {
            method: 'PUT',
            body: JSON.stringify(updates)
        });
    }
    async getSettings() {
        return this.request('/player/settings');
    }
    async updateSettings(settings) {
        return this.request('/player/settings', {
            method: 'PUT',
            body: JSON.stringify(settings)
        });
    }
    async getInventory() {
        return this.request('/player/inventory');
    }
    async getAchievements() {
        return this.request('/player/achievements');
    }
    // Game API methods (matching original /j/ endpoints)
    async initArea(urlName) {
        return this.request('/j/i/', {
            method: 'POST',
            body: JSON.stringify({ urlName })
        });
    }
    async getCreationDef(creationId) {
        return this.request(`/j/i/def/${creationId}`);
    }
    async createItem(itemData) {
        return this.request('/j/i/c/', {
            method: 'POST',
            body: JSON.stringify({ itemData })
        });
    }
    async getMapSectors(sectors, areaId, pane = 0) {
        return this.request('/j/m/s/', {
            method: 'POST',
            body: JSON.stringify({
                s: JSON.stringify(sectors),
                a: areaId,
                p: pane
            })
        });
    }
    async getSectorPlus(x, y, ap, aid) {
        return this.request(`/j/m/sp/${x}/${y}/${ap}/${aid}`);
    }
    async getFriendsAndBlocked() {
        return this.request('/j/u/fab/');
    }
    async getFreshRank() {
        return this.request('/j/u/gfr/', {
            method: 'POST'
        });
    }
    async unlockAchievement(achievementId) {
        return this.request('/j/u/a/', {
            method: 'POST',
            body: JSON.stringify({ id: achievementId })
        });
    }
    // Utility methods
    isAuthenticated() {
        return !!this.token;
    }
    getToken() {
        return this.token;
    }
    // Error handling helper
    handleApiError(response, defaultMessage = 'An error occurred') {
        if (!response.success) {
            console.error('API Error:', response.error, response.details);
            // You can integrate with your UI notification system here
            return response.error || defaultMessage;
        }
        return null;
    }
}
// Create a singleton instance
const apiService = new ApiService();
// Export for use in other modules
export default apiService;
// Also export the class for testing or multiple instances
export { ApiService };
