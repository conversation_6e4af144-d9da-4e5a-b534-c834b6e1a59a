{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": ".", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowJs": true, "moduleResolution": "node", "declaration": false, "sourceMap": true, "baseUrl": ".", "paths": {"*": ["node_modules/*"]}}, "include": ["server.ts", "src/**/*.ts"], "exclude": ["node_modules", "dist", "_code/**/*", "service-worker.ts"]}