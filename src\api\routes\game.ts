import { Router } from 'express';
import { z } from 'zod';
import { ObjectId } from 'mongodb';
import { db } from '../../server';
import { authenticateToken } from '../../server';

const router = Router();

// Area initialization - POST /j/i/
router.post('/i/', authenticateToken, async (req: any, res) => {
  try {
    const schema = z.object({
      urlName: z.string()
    });
    
    const { urlName } = schema.parse(req.body);
    
    // Get or create area data
    let areaData = await db.collection('area-data').findOne({ aun: urlName });
    
    if (!areaData) {
      // Create default area if it doesn't exist
      areaData = {
        aid: new ObjectId().toString(),
        gid: new ObjectId().toString(),
        sub: false,
        arn: urlName,
        agn: urlName,
        aun: urlName,
        ard: `Welcome to ${urlName}`,
        acl: { x: 0, y: 0 },
        apr: "000000000000000000000000", // Default area owner
        axx: false,
        aul: false,
        spe: false,
        ece: false,
        mpv: 1,
        createdAt: new Date()
      };
      
      await db.collection('area-data').insertOne(areaData);
    }
    
    // Get player init data
    const player = await db.collection('players').findOne({ 
      _id: new ObjectId(req.user.playerId) 
    });
    
    const initData = {
      area: areaData,
      player: {
        rid: player._id.toString(),
        age: Math.floor((Date.now() - player.createdAt.getTime()) / (1000 * 60 * 60 * 24)),
        ifa: player.isFullAccount,
        lma: player.leftMinfinityAmount,
        isb: player.isBacker,
        bbl: player.boostsLeft,
        hmf: player.hasMinfinity,
        stn: {} // Player settings
      }
    };
    
    res.json(initData);
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    console.error('Area init error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get creation definition - GET /j/i/def/:creationId
router.get('/i/def/:creationId', async (req, res) => {
  try {
    const { creationId } = req.params;
    
    if (!ObjectId.isValid(creationId)) {
      return res.status(400).json({ error: 'Invalid creation ID' });
    }
    
    const creation = await db.collection('creations').findOne({ 
      _id: new ObjectId(creationId) 
    });
    
    if (!creation) {
      return res.status(404).json({ error: 'Creation not found' });
    }
    
    res.json(creation);
    
  } catch (error) {
    console.error('Get creation definition error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create item - POST /j/i/c/
router.post('/i/c/', authenticateToken, async (req: any, res) => {
  try {
    const schema = z.object({
      itemData: z.object({
        name: z.string(),
        base: z.string(),
        props: z.record(z.any()).optional(),
        direction: z.number().optional()
      })
    });
    
    const { itemData } = schema.parse(req.body);
    
    const creation = {
      ...itemData,
      creator: req.user.playerId,
      createdAt: new Date(),
      likes: 0,
      collections: 0
    };
    
    const result = await db.collection('creations').insertOne(creation);
    
    res.json({ itemId: result.insertedId.toString() });
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    console.error('Create item error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get map sectors - POST /j/m/s/
router.post('/m/s/', authenticateToken, async (req: any, res) => {
  try {
    const schema = z.object({
      s: z.string(), // JSON string of sector coordinates
      a: z.string(), // Area ID
      p: z.number()  // Area pane
    });
    
    const { s, a, p } = schema.parse(req.body);
    const requestedSectors = JSON.parse(s);
    
    const sectorData = [];
    
    for (const [x, y] of requestedSectors) {
      const sector = await db.collection('area-sectors').findOne({
        areaId: a,
        x: x,
        y: y
      });
      
      if (sector) {
        sectorData.push(sector);
      }
    }
    
    res.json(sectorData);
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    console.error('Get map sectors error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get sector plus (3x3 grid) - GET /j/m/sp/:x/:y/:ap/:aid
router.get('/m/sp/:x/:y/:ap/:aid', async (req, res) => {
  try {
    const { x, y, ap, aid } = req.params;
    const centerX = parseInt(x);
    const centerY = parseInt(y);
    
    const sectors = [];
    
    // Get 3x3 grid of sectors around the center
    for (let dx = -1; dx <= 1; dx++) {
      for (let dy = -1; dy <= 1; dy++) {
        const sector = await db.collection('area-sectors').findOne({
          areaId: aid,
          x: centerX + dx,
          y: centerY + dy
        });
        
        if (sector) {
          sectors.push(sector);
        }
      }
    }
    
    res.json(sectors);
    
  } catch (error) {
    console.error('Get sector plus error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Friends and blocked - GET /j/u/fab/
router.get('/u/fab/', authenticateToken, async (req: any, res) => {
  try {
    const friendsData = await db.collection('player-friends').findOne({ 
      playerId: req.user.playerId 
    });
    
    res.json({
      friends: friendsData?.friends || [],
      blocked: friendsData?.blocked || []
    });
    
  } catch (error) {
    console.error('Get friends and blocked error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get fresh rank - POST /j/u/gfr/
router.post('/u/gfr/', authenticateToken, async (req: any, res) => {
  try {
    const player = await db.collection('players').findOne({ 
      _id: new ObjectId(req.user.playerId) 
    });
    
    res.json(player?.rank || 1);
    
  } catch (error) {
    console.error('Get fresh rank error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Achievement - POST /j/u/a/
router.post('/u/a/', authenticateToken, async (req: any, res) => {
  try {
    const schema = z.object({
      id: z.string()
    });
    
    const { id } = schema.parse(req.body);
    
    await db.collection('player-achievements').updateOne(
      { playerId: req.user.playerId },
      { 
        $addToSet: { 
          achievements: {
            id,
            unlockedAt: new Date()
          }
        }
      },
      { upsert: true }
    );
    
    res.json({ success: true });
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    console.error('Achievement error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
