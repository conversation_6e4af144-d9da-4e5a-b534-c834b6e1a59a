{
    "compilerOptions": {
        "strict": false,
        "target": "ES2020",
        "lib": [
            "ES2020",
            "DOM",
            "DOM.Iterable"
        ],
        "checkJs": true,
        "skipLibCheck": true,
        "module": "ES2020",
        "moduleResolution": "node",
        "isolatedModules": true,
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        "resolveJsonModule": true,
        "baseUrl": ".",
        "outDir": "./dist/client",
        "paths": {
            "*": [
                "node_modules/*"
            ]
        }
    },
    "include": [
        "_code/**/*.ts",
        "_code/**/*.mjs",
        "globals.d.ts"
    ],
    "exclude": [
        "_code/libs/*.js",
        "server.ts",
        "src/**/*",
        "node_modules",
        "dist"
    ]
}
// {
//     "compilerOptions": {
//         "strict": false,
//         "target": "ESNext",
//         "lib": [
//             "ESNext",
//             "DOM",
//             "DOM.Iterable",
//             "WebWorker"
//         ],
//         "skipLibCheck": true,
//         "module": "esnext",
//         "moduleResolution": "node",
//         "allowJs": true,
//         "esModuleInterop": true,
//         "resolveJsonModule": true,
//         "isolatedModules": false,
//         "baseUrl": ".",
//         "outDir": "./dist"
//     },
//     "include": [
//         "_code/service-worker/**/*",
//         "globals.d.ts",
//         "service-worker.ts"
//     ],
//     "exclude": [
//         "node_modules",
//         "dist"
//     ]
// }
// {
//     "compilerOptions": {
//         "strict": false,
//         "target": "ESNext",
//         "lib": [
//             "ESNext",
//             "DOM",
//             "DOM.Iterable",
//             "WebWorker"
//         ],
//         "skipLibCheck": true,
//         "moduleResolution": "node",
//         "allowJs": true,
//         "esModuleInterop": true,
//         "resolveJsonModule": true,
//         "isolatedModules": false,
//         "baseUrl": ".",
//         "composite": true,
//         "declaration": true,
//         "outDir": "./dist",
//         "rootDir": ".",
//         "paths": {
//             "*": ["node_modules/*"],
//             "@browser/*": ["_code/service-worker/*"],
//             "@server/*": ["./*"]
//         }
//     },
//     "include": [
//         "_code/service-worker/**/*.ts",
//         "globals.d.ts",
//         "service-worker.ts"
//     ],
//     "exclude": [
//         // "server.ts",
//         "node_modules",
//         "dist"
//     ]
// }