"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setDatabase = setDatabase;
const express_1 = require("express");
const zod_1 = require("zod");
const mongodb_1 = require("mongodb");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
// We'll get the db instance passed in
let db;
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
// Function to set the database instance
function setDatabase(database) {
    db = database;
}
// Authentication middleware
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }
    jsonwebtoken_1.default.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
}
const router = (0, express_1.Router)();
// Get player profile
router.get('/profile', authenticateToken, async (req, res) => {
    try {
        const player = await db.collection('players').findOne({
            _id: new mongodb_1.ObjectId(req.user.playerId)
        });
        if (!player) {
            return res.status(404).json({ error: 'Player not found' });
        }
        // Return profile data (excluding sensitive info like password)
        const { password, ...profileData } = player;
        res.json(profileData);
    }
    catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Update player profile
router.put('/profile', authenticateToken, async (req, res) => {
    try {
        const updateSchema = zod_1.z.object({
            screenName: zod_1.z.string().min(3).max(20).optional(),
            unfindable: zod_1.z.boolean().optional(),
            profileColor: zod_1.z.string().optional(),
            profileBackId: zod_1.z.string().optional(),
            profileDynaId: zod_1.z.string().optional()
        });
        const updates = updateSchema.parse(req.body);
        // Check if screenName is already taken (if updating screenName)
        if (updates.screenName) {
            const existingPlayer = await db.collection('players').findOne({
                screenName: updates.screenName,
                _id: { $ne: new mongodb_1.ObjectId(req.user.playerId) }
            });
            if (existingPlayer) {
                return res.status(400).json({ error: 'Screen name already taken' });
            }
        }
        const result = await db.collection('players').updateOne({ _id: new mongodb_1.ObjectId(req.user.playerId) }, { $set: { ...updates, updatedAt: new Date() } });
        if (result.matchedCount === 0) {
            return res.status(404).json({ error: 'Player not found' });
        }
        res.json({ success: true, message: 'Profile updated successfully' });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({ error: 'Invalid input', details: error.errors });
        }
        console.error('Update profile error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get player settings
router.get('/settings', authenticateToken, async (req, res) => {
    try {
        const settings = await db.collection('player-settings').findOne({
            playerId: req.user.playerId
        });
        // Return default settings if none exist
        const defaultSettings = {
            soundEnabled: true,
            musicEnabled: true,
            chatEnabled: true,
            showTutorial: true
        };
        res.json(settings?.settings || defaultSettings);
    }
    catch (error) {
        console.error('Get settings error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Update player settings
router.put('/settings', authenticateToken, async (req, res) => {
    try {
        const settingsSchema = zod_1.z.object({
            soundEnabled: zod_1.z.boolean().optional(),
            musicEnabled: zod_1.z.boolean().optional(),
            chatEnabled: zod_1.z.boolean().optional(),
            showTutorial: zod_1.z.boolean().optional()
        });
        const settings = settingsSchema.parse(req.body);
        await db.collection('player-settings').updateOne({ playerId: req.user.playerId }, {
            $set: {
                settings,
                updatedAt: new Date()
            }
        }, { upsert: true });
        res.json({ success: true, message: 'Settings updated successfully' });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({ error: 'Invalid input', details: error.errors });
        }
        console.error('Update settings error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get player inventory
router.get('/inventory', authenticateToken, async (req, res) => {
    try {
        const inventory = await db.collection('player-inventory').findOne({
            playerId: req.user.playerId
        });
        res.json(inventory?.items || []);
    }
    catch (error) {
        console.error('Get inventory error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Get player achievements
router.get('/achievements', authenticateToken, async (req, res) => {
    try {
        const achievements = await db.collection('player-achievements').findOne({
            playerId: req.user.playerId
        });
        res.json(achievements?.achievements || []);
    }
    catch (error) {
        console.error('Get achievements error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
// Award achievement
router.post('/achievements', authenticateToken, async (req, res) => {
    try {
        const achievementSchema = zod_1.z.object({
            id: zod_1.z.string(),
            name: zod_1.z.string(),
            description: zod_1.z.string()
        });
        const achievement = achievementSchema.parse(req.body);
        await db.collection('player-achievements').updateOne({ playerId: req.user.playerId }, {
            $addToSet: {
                achievements: {
                    ...achievement,
                    unlockedAt: new Date()
                }
            }
        }, { upsert: true });
        res.json({ success: true, message: 'Achievement unlocked!' });
    }
    catch (error) {
        if (error instanceof zod_1.z.ZodError) {
            return res.status(400).json({ error: 'Invalid input', details: error.errors });
        }
        console.error('Award achievement error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
exports.default = router;
//# sourceMappingURL=player.js.map