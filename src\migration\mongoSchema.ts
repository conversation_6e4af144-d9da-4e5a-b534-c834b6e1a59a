import { Db } from 'mongodb';

// MongoDB Collection Schemas based on existing IndexedDB structure

export interface PlayerDocument {
  _id?: string;
  rid: string; // Player ID
  screenName: string;
  password: string; // Hashed
  email?: string;
  isFullAccount: boolean;
  hasMinfinity: boolean;
  isBacker: boolean;
  rank: number;
  stat_ItemsPlaced: number;
  unfindable: boolean;
  ageDays: number;
  leftMinfinityAmount: number;
  boostsLeft: number;
  profileItemIds?: any;
  profileColor?: any;
  profileBackId?: any;
  profileDynaId?: any;
  createdAt: Date;
  lastLogin: Date;
  updatedAt?: Date;
}

export interface AreaDocument {
  _id?: string;
  aid: string; // Area ID
  gid: string; // Group ID
  sub: boolean;
  arn: string; // Area name
  agn: string; // Area group name
  aun: string; // Area URL name
  ard: string; // Area description
  acl: { x: number; y: number }; // Area center location
  iid?: string;
  adr?: { angle: number; speed: number };
  apr: string; // Area owner player ID
  axx: boolean;
  aul: boolean;
  spe: boolean;
  ece: boolean;
  mpv: number; // Map version
  createdAt: Date;
  updatedAt?: Date;
}

export interface SectorDocument {
  _id?: string;
  areaId: string;
  x: number;
  y: number;
  v: number; // Version
  iix: any[]; // Item indexes
  ps: [number, number][]; // Placements
  i: {
    b: any[]; // Blocks
    p: any[]; // Placements
    n: any[]; // Names
    dr: any[]; // Directions
  };
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreationDocument {
  _id?: string;
  name: string;
  base: string;
  creator: string; // Player ID
  props?: Record<string, any>;
  direction?: number;
  likes: number;
  collections: number;
  createdAt: Date;
  updatedAt?: Date;
}

export interface MultiDocument {
  _id?: string;
  creationId: string;
  data: any;
  createdAt: Date;
  updatedAt?: Date;
}

export interface MotionsOfBodyDocument {
  _id?: string;
  bodyId: string;
  midpoint: number;
  ids: string[];
  createdAt: Date;
  updatedAt?: Date;
}

export interface HolderDocument {
  _id?: string;
  holderId: string;
  isCreator: boolean;
  contents: {
    _id: string;
    itemId: string;
    x: number;
    y: number;
    z: number;
    flip: number;
    rot: number;
    pageNo: number;
  }[];
  createdAt: Date;
  updatedAt?: Date;
}

export interface CreationStatsDocument {
  _id?: string;
  creationId: string;
  timesCd: number; // Times created
  timesPd: number; // Times placed
  createdAt: Date;
  updatedAt?: Date;
}

export interface PainterDataDocument {
  _id?: string;
  creationId: string;
  data: any;
  createdAt: Date;
  updatedAt?: Date;
}

export interface PlayerInventoryDocument {
  _id?: string;
  playerId: string;
  items: any[];
  createdAt: Date;
  updatedAt?: Date;
}

export interface PlayerAchievementsDocument {
  _id?: string;
  playerId: string;
  achievements: {
    id: string;
    name?: string;
    description?: string;
    unlockedAt: Date;
  }[];
  createdAt: Date;
  updatedAt?: Date;
}

export interface PlayerSettingsDocument {
  _id?: string;
  playerId: string;
  settings: {
    soundEnabled: boolean;
    musicEnabled: boolean;
    chatEnabled: boolean;
    showTutorial: boolean;
    [key: string]: any;
  };
  createdAt: Date;
  updatedAt?: Date;
}

export interface PlayerFriendsDocument {
  _id?: string;
  playerId: string;
  friends: string[];
  blocked: string[];
  createdAt: Date;
  updatedAt?: Date;
}

export interface PlayerPositionDocument {
  _id?: string;
  playerId: string;
  x: number;
  y: number;
  areaId: string;
  updatedAt: Date;
}

export interface ChatMessageDocument {
  _id?: string;
  playerId: string;
  playerName: string;
  areaId: string;
  message: string;
  timestamp: Date;
}

// Database initialization and schema setup
export async function initializeDatabase(db: Db) {
  console.log('Initializing MongoDB collections and indexes...');

  // Create collections if they don't exist
  const collections = [
    'players',
    'area-data',
    'area-sectors',
    'creations',
    'multis',
    'motions-of-body',
    'holders',
    'creation-stats',
    'creation-painter-data',
    'player-inventory',
    'player-achievements',
    'player-settings',
    'player-friends',
    'player-positions',
    'chat-messages',
    'player-top-creations',
    'minimap-colors',
    'minimap-area-tile-ids'
  ];

  for (const collectionName of collections) {
    try {
      await db.createCollection(collectionName);
      console.log(`Created collection: ${collectionName}`);
    } catch (error: any) {
      if (error.code !== 48) { // Collection already exists
        console.error(`Error creating collection ${collectionName}:`, error);
      }
    }
  }

  // Create indexes for better performance
  await createIndexes(db);
  
  console.log('Database initialization complete');
}

async function createIndexes(db: Db) {
  try {
    // Player indexes
    await db.collection('players').createIndex({ 'rid': 1 }, { unique: true });
    await db.collection('players').createIndex({ 'screenName': 1 }, { unique: true });
    await db.collection('players').createIndex({ 'email': 1 }, { sparse: true });
    
    // Area indexes
    await db.collection('area-data').createIndex({ 'aid': 1 }, { unique: true });
    await db.collection('area-data').createIndex({ 'gid': 1 });
    await db.collection('area-data').createIndex({ 'aun': 1 }, { unique: true });
    await db.collection('area-data').createIndex({ 'apr': 1 });
    
    // Sector indexes
    await db.collection('area-sectors').createIndex({ 'areaId': 1, 'x': 1, 'y': 1 }, { unique: true });
    await db.collection('area-sectors').createIndex({ 'areaId': 1 });
    
    // Creation indexes
    await db.collection('creations').createIndex({ 'creator': 1 });
    await db.collection('creations').createIndex({ 'createdAt': -1 });
    await db.collection('creations').createIndex({ 'likes': -1 });
    
    // Multi indexes
    await db.collection('multis').createIndex({ 'creationId': 1 });
    
    // Motion indexes
    await db.collection('motions-of-body').createIndex({ 'bodyId': 1 }, { unique: true });
    
    // Holder indexes
    await db.collection('holders').createIndex({ 'holderId': 1 }, { unique: true });
    
    // Stats indexes
    await db.collection('creation-stats').createIndex({ 'creationId': 1 }, { unique: true });
    
    // Painter data indexes
    await db.collection('creation-painter-data').createIndex({ 'creationId': 1 }, { unique: true });
    
    // Player-related indexes
    await db.collection('player-inventory').createIndex({ 'playerId': 1 }, { unique: true });
    await db.collection('player-achievements').createIndex({ 'playerId': 1 }, { unique: true });
    await db.collection('player-settings').createIndex({ 'playerId': 1 }, { unique: true });
    await db.collection('player-friends').createIndex({ 'playerId': 1 }, { unique: true });
    await db.collection('player-positions').createIndex({ 'playerId': 1 }, { unique: true });
    await db.collection('player-positions').createIndex({ 'areaId': 1 });
    
    // Chat indexes
    await db.collection('chat-messages').createIndex({ 'areaId': 1, 'timestamp': -1 });
    await db.collection('chat-messages').createIndex({ 'playerId': 1 });
    
    // Top creations indexes
    await db.collection('player-top-creations').createIndex({ 'playerId': 1 }, { unique: true });
    
    // Minimap indexes
    await db.collection('minimap-colors').createIndex({ 'areaId': 1 });
    await db.collection('minimap-area-tile-ids').createIndex({ 'areaId': 1 });
    
    console.log('Database indexes created successfully');
  } catch (error) {
    console.error('Error creating indexes:', error);
  }
}
