import apiService from "./client/apiService.js";
import websocketService from "./client/websocketService.js";
import * as FilePond from "./libs/filepond.esm.min.js";
import FilePondPluginFileValidateType from "./libs/filepond-plugin-file-validate-type.esm.min.js";
import Toastify from "./libs/toastify-es.js";

const { z } = /** @type { import('zod' )} */ (globalThis.Zod);
const { el, text, mount, setChildren, setAttr, list } = /**@type { import('redom' )} */ (globalThis.redom);

// Toast utilities
const TOAST_SETTINGS = { duration: 5000, position: "right" };
const toastError = (msg) => Toastify({ ...TOAST_SETTINGS, style: { background: "linear-gradient(to right, #e46161, #d32424)" }, text: msg }).showToast();
const toastSuccess = (msg) => Toastify({ ...TOAST_SETTINGS, style: { background: "linear-gradient(to right, #00b09b, #96c93d)" }, text: msg }).showToast();

class LoginModal {
    constructor() {
        this.el = el("dialog.modal", [
            el('div.modal-box', [
                el('h3.font-bold.text-lg', 'Login to Xnafu'),
                el('form', { onsubmit: (e) => this.handleLogin(e) }, [
                    el('div.form-control.w-full.max-w-xs', [
                        el('label.label', [
                            el('span.label-text', 'Screen Name')
                        ]),
                        this.screenNameInput = el('input.input.input-bordered.w-full.max-w-xs', { 
                            type: 'text', 
                            placeholder: 'Your screen name',
                            required: true
                        })
                    ]),
                    el('div.form-control.w-full.max-w-xs', [
                        el('label.label', [
                            el('span.label-text', 'Password')
                        ]),
                        this.passwordInput = el('input.input.input-bordered.w-full.max-w-xs', { 
                            type: 'password', 
                            placeholder: 'Your password',
                            required: true
                        })
                    ]),
                    el('div.modal-action', [
                        el('button.btn.btn-primary', { type: 'submit' }, 'Login'),
                        el('button.btn', { 
                            type: 'button',
                            onclick: () => this.showRegister()
                        }, 'Register'),
                        el('button.btn', { 
                            type: 'button',
                            onclick: () => this.el.close()
                        }, 'Cancel')
                    ])
                ])
            ])
        ]);
    }

    async handleLogin(e) {
        e.preventDefault();
        const screenName = this.screenNameInput.value;
        const password = this.passwordInput.value;

        try {
            const response = await apiService.login(screenName, password);
            if (response.success) {
                toastSuccess('Logged in successfully!');
                this.el.close();
                // Refresh the interface
                mainInterface.checkAuthAndUpdate();
            } else {
                toastError(response.error || 'Login failed');
            }
        } catch (error) {
            toastError('Login failed: ' + error.message);
        }
    }

    showRegister() {
        // For now, just show a simple prompt
        // TODO: Create a proper register modal
        const screenName = prompt('Enter screen name:');
        const password = prompt('Enter password:');
        
        if (screenName && password) {
            this.handleRegister(screenName, password);
        }
    }

    async handleRegister(screenName, password) {
        try {
            const response = await apiService.register(screenName, password);
            if (response.success) {
                toastSuccess('Registered and logged in successfully!');
                this.el.close();
                mainInterface.checkAuthAndUpdate();
            } else {
                toastError(response.error || 'Registration failed');
            }
        } catch (error) {
            toastError('Registration failed: ' + error.message);
        }
    }

    showModal() {
        this.el.showModal();
    }
}

class AreaCard {
    constructor() {
        this.el = el("div.w-40.md:w-56.rounded.overflow-hidden.shadow-lg", [
            this.cardThumb = el("img.w-full.rounded.cursor-pointer", {
                alt: "Area thumbnail",
                onclick: () => this.onBtnClick(),
            }),
            el("div.p-2.text-center.break-words.h-20.md:h-16", [
                el("div.font-bold.text-xl.mb-2", this.cardTitle = text())
            ]),
            this.btnSpot = el("div.flex.justify-center.pb-4")
        ]);
    }

    onBtnClick() {
        if (this.status === "AVAILABLE") {
            // Navigate to the area
            window.location.href = "/game?area=" + this.areaUrlName;
        }
    }

    update({ areaUrlName, areaRealName, status }) {
        this.areaUrlName = areaUrlName;
        this.areaRealName = areaRealName;
        this.status = status;

        if (status === "AVAILABLE") {
            const btn = el("a.w-36.h-10.text-center.bg-blue-500.hover:bg-blue-700.text-white.font-bold.py-2.px-4.rounded", 
                { href: "/game?area=" + areaUrlName }, 
                ["Play"]
            );
            this.cardThumb.src = `/static/data/area-thumbnails/${this.areaUrlName}.png`;
            setChildren(this.btnSpot, [btn]);
        } else {
            const btn = el("button.w-36.h-10.bg-gray-500.text-white.font-bold.py-2.px-4.rounded", 
                { disabled: true }, 
                "Coming Soon"
            );
            this.cardThumb.src = `/static/data/area-thumbnails/3.png`;
            setChildren(this.btnSpot, [btn]);
        }

        this.cardTitle.textContent = this.areaRealName;
    }
}

class CreateNewAreaBtn {
    constructor() {
        this.el = el("div.py-4.mx-auto.rounded.overflow-hidden.shadow-lg", [
            el("button.w-36.h-10.text-center.bg-green-500.hover:bg-green-700.text-white.font-bold.py-2.px-4.rounded", 
                { onclick: () => this.onBtnClick() }, 
                ["Create Area"]
            )
        ]);
    }

    async onBtnClick() {
        if (!apiService.isAuthenticated()) {
            toastError('Please login first');
            loginModal.showModal();
            return;
        }

        const areaName = prompt("Area name:");
        if (!areaName) return;

        try {
            // TODO: Implement area creation API
            toastSuccess('Area creation coming soon!');
        } catch (error) {
            toastError('Failed to create area: ' + error.message);
        }
    }
}

class MainInterface {
    constructor() {
        this.areaListEl = list("div.gap-1.md:gap-4.flex.flex-row.flex-wrap.justify-around", AreaCard);
        this.el = el("div.flex.flex-col.justify-center", [
            el("span.loading.loading-spinner.loading-lg.pt-52"),
        ]);
    }

    async checkAuthAndUpdate() {
        if (apiService.isAuthenticated()) {
            // Try to connect WebSocket
            try {
                await websocketService.connect(apiService.getToken());
                console.log('WebSocket connected');
            } catch (error) {
                console.warn('WebSocket connection failed:', error);
            }
        }
        
        this.loadAreas();
    }

    async loadAreas() {
        try {
            // For now, show some default areas
            // TODO: Load from API
            const defaultAreas = [
                { areaUrlName: "welcome", areaRealName: "Welcome Area", status: "AVAILABLE" },
                { areaUrlName: "sandbox", areaRealName: "Sandbox", status: "AVAILABLE" },
                { areaUrlName: "gemcastle", areaRealName: "Gem Castle", status: "COMING_SOON" }
            ];

            this.update({ state: "AREALIST", areas: defaultAreas });
        } catch (error) {
            console.error('Failed to load areas:', error);
            this.update({ state: "ERROR", error: "Unable to load area list." });
        }
    }

    update(data) {
        if (data.state === "LOADING") {
            setChildren(this.el, [
                el("span.loading.loading-spinner.loading-lg.pt-52"),
            ]);
        }
        else if (data.state === "AREALIST") {
            setChildren(this.el, [
                new CreateNewAreaBtn(),
                this.areaListEl
            ]);
            this.areaListEl.update(data.areas);
        }
        else if (data.state === "ERROR") {
            setChildren(this.el, [
                el("div.alert.alert-error", { role: "alert" }, [
                    el("svg.stroke-current.shrink-0.h-6.w-6", {
                        xmlns: "http://www.w3.org/2000/svg",
                        fill: "none",
                        viewBox: "0 0 24 24"
                    }, [
                        el("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: "2",
                            d: "M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                        })
                    ]),
                    el("span", data.error || "Something went wrong!")
                ])
            ]);
        }
    }
}

// Global instances
const loginModal = new LoginModal();
const mainInterface = new MainInterface();

// Main content
const main = el("main", [
    el("div.grid.md:grid-cols-5", [
        el("div.md:col-span-4", [
            el("h2.text-2xl.text-center", "Online Areas"),
            mainInterface,
        ]),
        el("div.p-4", [
            el("div", [
                el("h2.text-2xl.text-center", "Account"),
                el("div.p-4", [
                    el("button.btn.btn-primary", { 
                        onclick: () => {
                            if (apiService.isAuthenticated()) {
                                apiService.logout();
                                websocketService.disconnect();
                                toastSuccess('Logged out successfully');
                                mainInterface.checkAuthAndUpdate();
                            } else {
                                loginModal.showModal();
                            }
                        }
                    }, "Login/Logout")
                ])
            ]),
            el("div.border-b-1.border-black"),
            el("div.mt-6", [
                el("h2.text-2xl.text-center", "Coming Soon"),
                el("p", "More features will be added as we transition to the online architecture!"),
            ])
        ]),
    ]),
    loginModal
]);

// Initialize the application
document.addEventListener("DOMContentLoaded", async () => {
    mount(document.getElementById("app"), main);
    
    // Check if user is already logged in
    if (apiService.isAuthenticated()) {
        try {
            const response = await apiService.verifyToken();
            if (!response.success) {
                // Token is invalid, clear it
                apiService.logout();
            }
        } catch (error) {
            console.warn('Token verification failed:', error);
            apiService.logout();
        }
    }

    // Initialize the interface
    await mainInterface.checkAuthAndUpdate();
});

// Error handling
document.addEventListener("unhandledrejection", (event) => {
    console.error("Unhandled promise rejection:", event);
    toastError(`Something went wrong: ${String(event.reason)}`);
});

document.addEventListener("error", (event) => {
    console.error("Global error:", event);
    toastError(`Something went wrong: ${String(event.error)}`);
});

export { mainInterface, loginModal, toastError, toastSuccess };
