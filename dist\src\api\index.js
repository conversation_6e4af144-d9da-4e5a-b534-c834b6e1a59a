"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeRoutes = initializeRoutes;
const express_1 = require("express");
const auth_1 = __importStar(require("./routes/auth"));
const player_1 = __importStar(require("./routes/player"));
const game_1 = __importStar(require("./routes/game"));
const router = (0, express_1.Router)();
// Function to initialize routes with database
function initializeRoutes(database) {
    (0, auth_1.setDatabase)(database);
    (0, player_1.setDatabase)(database);
    (0, game_1.setDatabase)(database);
}
// API Routes
router.use('/auth', auth_1.default);
router.use('/player', player_1.default);
// Game API routes (matching the original /j/ pattern)
router.use('/j', game_1.default);
// Health check endpoint
router.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});
// Catch-all for unmatched API routes
router.use('*', (req, res) => {
    res.status(404).json({
        error: 'API endpoint not found',
        path: req.originalUrl,
        method: req.method
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map