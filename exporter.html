<!DOCTYPE html>
<html lang="en">
    <head>
        <title>Offlineland</title>
        <link href="/static/offlineland/style.css" rel="stylesheet" />
        <link href="/_code/libs/toastify.css" rel="stylesheet" />
    </head>
    <body>
        <script type="module">
            import Toastify from "/_code/libs/toastify-es.js"
            const TOAST_SETTINGS = { duration: 5000, position: "right" };
            const toastError =   (msg) => Toastify({ ...TOAST_SETTINGS, style: { background: "linear-gradient(to right, #e46161, #d32424)" }, text: msg }).showToast();
            const toastSuccess = (msg) => Toastify({ ...TOAST_SETTINGS, style: { background: "linear-gradient(to right, #00b09b, #96c93d)" }, text: msg }).showToast();



            function copyToClipboard(elementId) {
                const cb = document.getElementById(elementId);
                const text = cb.innerText;
                navigator.clipboard.writeText(text).then(() => {
                    toastSuccess('Copied to clipboard!');
                }).catch(err => {
                    console.error('Error in copying text: ', err);
                    toastError('Error in copying text')
                });
            }

            window.copyToClipboard = copyToClipboard;
        </script>

        <nav class="navbar">
            <div class="navbar-start">

                <a href="/" class="btn btn-ghost gap-1 h-2 min-h-[2rem] px-2 mx-1 ">
                    <img style="image-rendering:pixelated" src="/static/offlineland/favicons/unplugged-1-grey.png" class="w-6 h-6" />
                    <span class="font-title text-base-content text-lg md:text-xl">offlineland.io</span>
                </a>
            </div>

            <div class="navbar-center">
                <!-- todo: move that to navbar-end, use a rightside button, figure out how to not have the menu go beyond the screen-->
                <div class="dropdown">
                    <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16" />
                        </svg>

                        <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                            <li>
                                <details class="">
                                    <summary class="py-1">
                                        Museum
                                    </summary>
                                    <ul class="p-2 bg-base-100 rounded-t-none">
                                        <li class="disabled"><a>
                                            Maps
                                            <span class="badge badge-sm badge-primary">Soon!</span>
                                        </a></li>
                                        <li class="disabled"><a>
                                            Sounds
                                            <span class="badge badge-sm badge-primary">Soon!</span>
                                        </a></li>
                                        <li class="disabled"><a>
                                            Universe Search
                                            <span class="badge badge-sm badge-primary">Soon!</span>
                                        </a></li>
                                    </ul>
                                </details>
                            </li>
                            <li class="disabled"><a class="py-1">
                                Achievements
                                <span class="badge badge-sm badge-primary">Soon!</span>
                            </a></li>
                            <li><a class="py-1" href="/exporter">Exporter</a></li>
                            <li><a class="py-1" href="https://online.offlineland.io">Onlineland</a></li>
                        </ul>
                    </div>
                </div>
                

            </div>

            <div class="navbar-end hidden lg:flex">
                <ul class="menu menu-horizontal px-1">
                    <li>
                        <details class="">
                            <summary class="py-1">
                                Museum
                            </summary>
                            <ul class="p-2 bg-base-100 rounded-t-none">
                                <li class="disabled"><a>
                                    Maps
                                    <span class="badge badge-sm badge-primary">Soon!</span>
                                </a></li>
                                <li class="disabled"><a>
                                    Sounds
                                    <span class="badge badge-sm badge-primary">Soon!</span>
                                </a></li>
                                <li class="disabled"><a>
                                    Universe Search
                                    <span class="badge badge-sm badge-primary">Soon!</span>
                                </a></li>
                            </ul>
                        </details>
                    </li>
                    <li class="disabled"><a class="py-1">
                        Achievements
                        <span class="badge badge-sm badge-primary">Soon!</span>
                    </a></li>
                    <li><a class="py-1" href="/exporter">Exporter</a></li>
                    <li><a class="py-1" href="https://online.offlineland.io">Onlineland</a></li>
                </ul>
            </div>
        </nav>


        <main class="m-3 md:m-6">
            <div class="text-center pb-6">
                <h1 class="text-4xl font-bold"><a href="https://offlineland.io">Offlineland.io</a> exporter</h1>
                <p>
                    Finish a previously-started profile export
                </p>
            </div>

            <div class="p-3 m-3">
                <h2 id="instructions" class="text-xl md:pb-3">Instructions:</h2>
                <ul class="md:px-6 space-y-2">
                    <li class="flex space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                        </svg>

                        <p>
                            Manyland has closed down! You can't export your profile anymore (but you can still continue or re-download a previously-started export)
                        </p>
                    </li>
                    <li class="flex space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M11.35 3.836c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m8.9-4.414c.376.023.75.05 1.124.08 1.131.094 1.976 1.057 1.976 2.192V16.5A2.25 2.25 0 0118 18.75h-2.25m-7.5-10.5H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V18.75m-7.5-10.5h6.375c.621 0 1.125.504 1.125 1.125v9.375m-8.25-3l1.5 1.5 3-3.75" />
                        </svg>

                        <p>
                            Click "Copy to Clipboard" below
                        </p>
                    </li>
                    <li class="flex space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M13.19 8.688a4.5 4.5 0 011.242 7.244l-4.5 4.5a4.5 4.5 0 01-6.364-6.364l1.757-1.757m13.35-.622l1.757-1.757a4.5 4.5 0 00-6.364-6.364l-4.5 4.5a4.5 4.5 0 001.242 7.244" />
                        </svg>

                        <p>
                            Go to <a href="https://manyland.com/" class="text-blue-500 hover:text-blue-700 underline" target="_blank">https://manyland.com/</a>
                        </p>
                    </li>
                    <li class="flex space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 7.5l3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0021 18V6a2.25 2.25 0 00-2.25-2.25H5.25A2.25 2.25 0 003 6v12a2.25 2.25 0 002.25 2.25z" />
                        </svg>

                        <p>
                            Open the console by pressing F12
                        </p>
                    </li>
                    <li class="flex space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M11.35 3.836c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m8.9-4.414c.376.023.75.05 1.124.08 1.131.094 1.976 1.057 1.976 2.192V16.5A2.25 2.25 0 0118 18.75h-2.25m-7.5-10.5H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V18.75m-7.5-10.5h6.375c.621 0 1.125.504 1.125 1.125v9.375m-8.25-3l1.5 1.5 3-3.75" />
                        </svg>

                        <p>
                            Paste the snippet (if that's the first time you're doing this, it'll ask you to type something in first) and press enter
                        </p>
                    </li>
                    <li class="flex space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-primary">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z" />
                        </svg>

                        <p>
                            Click the button, and wait. The zip should download automatically.
                        </p>
                    </li>
                </ul>
            </div>


            <div class="border-b border-primary m-3 pt-3"></div>


            <div class="flex flex-col p-4 join join-vertical">
                <button onclick="copyToClipboard('codeBlock')" class="join-item bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Copy to Clipboard
                </button>
                <pre id="codeBlock" class="join-item p-4 bg-gray-100 rounded-md overflow-auto">fetch("https://raw.githubusercontent.com/offlineland/exporter/main/exporter.js").then(res => res.text()).then(txt => eval(txt));</pre>
            </div>


            <div class="border-b border-primary m-3 pt-3"></div>


            <div class="p-3 m-3">
                <p>
                    Notes:
                    <ul class="px-6 list-disc">
                        <li>Data is saved locally between runs. You can re-run the archiver at any point and it'll pick back up where it left off.</li>
                        <li>Downloading collected creations will take a little while.</li>
                        <li>Since Manyland is now offline, this downloads data from the archives. It will only be able to save public things.</li>
                        <li>As usual, you shouldn't run code from sources you do not trust, yadda yadda</li>
                        <li>The code is also available <a class="link link-secondary" href="https://github.com/offlineland/exporter">on Github</a>.</li>
                    </ul>
                </p>
            </div>
        </main>

        <footer class="footer footer-center p-4 bg-neutral text-neutral-content">
            <div class="text-xs opacity-80">
                <p>offlineland.io is not affiliated with manyland.com or its developers.</p>
                <p>If you need help, please send us a mail at <a class="link" href="mailto:<EMAIL>"><EMAIL></a>!</p>
            </div>
        </footer>
    </body>
</html>
