import { Router, Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { ObjectId, Db } from 'mongodb';
import jwt from 'jsonwebtoken';

// We'll get the db instance passed in
let db: Db;
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Function to set the database instance
export function setDatabase(database: Db) {
  db = database;
}

// Authentication middleware
function authenticateToken(req: any, res: Response, next: NextFunction) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err: any, user: any) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
}

const router = Router();

// Get player profile
router.get('/profile', authenticateToken, async (req: any, res: Response) => {
  try {
    const player = await db.collection('players').findOne({
      _id: new ObjectId(req.user.playerId)
    });

    if (!player) {
      return res.status(404).json({ error: 'Player not found' });
    }

    // Return profile data (excluding sensitive info like password)
    const { password, ...profileData } = player;
    res.json(profileData);

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update player profile
router.put('/profile', authenticateToken, async (req: any, res: Response) => {
  try {
    const updateSchema = z.object({
      screenName: z.string().min(3).max(20).optional(),
      unfindable: z.boolean().optional(),
      profileColor: z.string().optional(),
      profileBackId: z.string().optional(),
      profileDynaId: z.string().optional()
    });

    const updates = updateSchema.parse(req.body);

    // Check if screenName is already taken (if updating screenName)
    if (updates.screenName) {
      const existingPlayer = await db.collection('players').findOne({
        screenName: updates.screenName,
        _id: { $ne: new ObjectId(req.user.playerId) }
      });

      if (existingPlayer) {
        return res.status(400).json({ error: 'Screen name already taken' });
      }
    }

    const result = await db.collection('players').updateOne(
      { _id: new ObjectId(req.user.playerId) },
      { $set: { ...updates, updatedAt: new Date() } }
    );

    if (result.matchedCount === 0) {
      return res.status(404).json({ error: 'Player not found' });
    }

    res.json({ success: true, message: 'Profile updated successfully' });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    console.error('Update profile error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get player settings
router.get('/settings', authenticateToken, async (req: any, res: Response) => {
  try {
    const settings = await db.collection('player-settings').findOne({
      playerId: req.user.playerId
    });

    // Return default settings if none exist
    const defaultSettings = {
      soundEnabled: true,
      musicEnabled: true,
      chatEnabled: true,
      showTutorial: true
    };

    res.json(settings?.settings || defaultSettings);

  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update player settings
router.put('/settings', authenticateToken, async (req: any, res: Response) => {
  try {
    const settingsSchema = z.object({
      soundEnabled: z.boolean().optional(),
      musicEnabled: z.boolean().optional(),
      chatEnabled: z.boolean().optional(),
      showTutorial: z.boolean().optional()
    });

    const settings = settingsSchema.parse(req.body);

    await db.collection('player-settings').updateOne(
      { playerId: req.user.playerId },
      {
        $set: {
          settings,
          updatedAt: new Date()
        }
      },
      { upsert: true }
    );

    res.json({ success: true, message: 'Settings updated successfully' });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    console.error('Update settings error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get player inventory
router.get('/inventory', authenticateToken, async (req: any, res: Response) => {
  try {
    const inventory = await db.collection('player-inventory').findOne({
      playerId: req.user.playerId
    });

    res.json(inventory?.items || []);

  } catch (error) {
    console.error('Get inventory error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get player achievements
router.get('/achievements', authenticateToken, async (req: any, res: Response) => {
  try {
    const achievements = await db.collection('player-achievements').findOne({
      playerId: req.user.playerId
    });

    res.json(achievements?.achievements || []);

  } catch (error) {
    console.error('Get achievements error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Award achievement
router.post('/achievements', authenticateToken, async (req: any, res: Response) => {
  try {
    const achievementSchema = z.object({
      id: z.string(),
      name: z.string(),
      description: z.string()
    });

    const achievement = achievementSchema.parse(req.body);

    await db.collection('player-achievements').updateOne(
      { playerId: req.user.playerId },
      {
        $addToSet: {
          achievements: {
            ...achievement,
            unlockedAt: new Date()
          }
        }
      },
      { upsert: true }
    );

    res.json({ success: true, message: 'Achievement unlocked!' });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    console.error('Award achievement error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
