# Architectural Transition Plan: IndexedDB to MongoDB with Real APIs

## Executive Summary

This document outlines the complete migration strategy from the current IndexedDB + Service Worker FakeAPI architecture to a MongoDB + Real API server architecture for Xnafuland. The transition will enable true multiplayer functionality while maintaining existing game features.

## Current Architecture Analysis

### Current Stack
- **Frontend**: Game client using manyland.js (v3568) with custom WebSocket wrapper
- **Data Storage**: IndexedDB via `LocalMLDatabase` class with 8 main object stores
- **API Layer**: Service Worker with `FakeAPI` simulating Manyland's `/j/` endpoints
- **Communication**: Fake WebSocket class relaying messages through Service Worker
- **Dependencies**: Express.js, MongoDB drivers, WebSocket library already installed

### Current Data Models (IndexedDB Schema)

```typescript
interface OfflinelandIDBSchema {
    "area-data": AreaData;           // Area metadata and configuration
    "area-sectors": SectorData;      // Game world sector data with placements
    "multis": MultiData;             // Multi-block creation data
    "holders": HolderData;           // Container/inventory data
    "motions-of-body": MotionsOfBody; // Animation/movement data
    "creation-stats": CreationStats;  // Creation usage statistics
    "creation-painter-data": PainterData; // Visual creation data
    "minimap-colors": string;        // Minimap color cache
    "minimap-area-tile-ids": string; // Minimap tile references
}
```

### Current API Surface Area (Service Worker FakeAPI)

**User Management**
- `/j/u/fab/` - Friends and blocked users
- `/j/u/gfr/` - Get fresh rank
- `/j/u/a/` - Achievements

**Items & Creations**
- `/j/i/st/:creationId` - Get creation stats
- `/j/i/c/` - Create new item
- `/j/i/g/:creationId` - Get creation data
- `/j/i/s/:creationId` - Save creation

**Map & World**
- `/j/m/cmv/` - Created map version
- `/j/m/sp/:x/:y/:ap/:aid` - Get sector plus (9-sector grid)
- `/j/m/s/:x/:y/:aid` - Get single sector
- `/j/m/p/` - Place item in world

**Player Data**
- `/j/p/gd/` - Get player data
- `/j/p/sd/` - Save player data
- `/j/p/gi/` - Get inventory

**Areas**
- `/j/a/g/:areaId` - Get area data
- `/j/a/e/:areaId` - Enter area

## Target Architecture

### New Stack
- **Backend**: Node.js + Express.js API server
- **Database**: MongoDB with Mongoose ODM
- **Real-time**: WebSocket server for multiplayer
- **Frontend**: Direct API calls (no service worker)
- **Authentication**: JWT-based user sessions

### MongoDB Schema Design

```javascript
// Areas Collection
const areaSchema = {
    _id: ObjectId,
    aid: String,           // Area ID
    gid: String,           // Game ID
    arn: String,           // Area name
    agn: String,           // Area group name
    aun: String,           // Area username
    ard: String,           // Area description
    acl: { x: Number, y: Number }, // Area center location
    settings: {
        sub: Boolean,      // Subscription required
        axx: Boolean,      // Area exclusive
        aul: Boolean,      // Area unlisted
        spe: Boolean,      // Special area
        ece: Boolean,      // Economy enabled
        mpv: Number        // Max players
    },
    createdAt: Date,
    updatedAt: Date
};

// Sectors Collection (for world data)
const sectorSchema = {
    _id: ObjectId,
    areaId: String,        // Reference to area
    x: Number,             // Sector X coordinate
    y: Number,             // Sector Y coordinate
    version: Number,       // Data version
    placements: [{         // Items placed in this sector
        x: Number,         // Local X position
        y: Number,         // Local Y position
        z: Number,         // Z-layer
        creationId: String, // Item/creation ID
        rotation: Number,   // Rotation angle
        flip: Number,       // Flip state
        placedBy: String,   // Player ID
        placedAt: Date
    }],
    items: {               // Special items (bodies, etc.)
        bodies: [String],
        props: [Object],
        names: [String],
        directions: [Number]
    },
    updatedAt: Date
};

// Users Collection
const userSchema = {
    _id: ObjectId,
    username: String,
    email: String,
    passwordHash: String,
    profile: {
        screenName: String,
        rank: Number,
        isFullAccount: Boolean,
        hasMinfinity: Boolean,
        isBacker: Boolean,
        ageDays: Number,
        unfindable: Boolean,
        stats: {
            itemsPlaced: Number,
            timesCd: Number,    // Times created
            timesPd: Number     // Times placed
        }
    },
    gameData: {
        leftMinfinityAmount: Number,
        boostsLeft: Number,
        currentArea: String,
        position: { x: Number, y: Number }
    },
    createdAt: Date,
    lastActive: Date
};

// Creations Collection
const creationSchema = {
    _id: ObjectId,
    creationId: String,    // Unique creation identifier
    name: String,
    createdBy: String,     // User ID
    data: {
        base: String,      // Base sprite/template
        direction: Number,
        props: Object,     // Creation properties
        painterData: Object // Visual customization
    },
    stats: {
        timesCd: Number,
        timesPd: Number
    },
    createdAt: Date,
    updatedAt: Date
};
```

## Migration Strategy

### Phase 1: Infrastructure Setup
1. **MongoDB Setup**
   - Install and configure MongoDB instance
   - Create database and collections with proper indexing
   - Set up connection pooling and error handling

2. **API Server Foundation**
   - Create Express.js server structure
   - Set up middleware (CORS, body parsing, logging)
   - Implement basic routing structure
   - Add environment configuration

### Phase 2: Data Migration
1. **Migration Scripts**
   - Create scripts to export IndexedDB data
   - Transform data to match MongoDB schema
   - Implement data validation and integrity checks
   - Create rollback procedures

2. **Dual-Write System**
   - Temporarily write to both IndexedDB and MongoDB
   - Validate data consistency
   - Gradual migration of read operations

### Phase 3: API Development
1. **Core API Endpoints**
   - Replace FakeAPI endpoints with real implementations
   - Implement proper error handling and validation
   - Add request/response logging
   - Create API documentation

2. **Authentication System**
   - Implement JWT-based authentication
   - Create user registration/login endpoints
   - Add session management
   - Implement authorization middleware

### Phase 4: Real-time Features
1. **WebSocket Server**
   - Replace service worker message system
   - Implement room-based connections (per area)
   - Add player synchronization
   - Handle connection management

2. **Multiplayer Logic**
   - Real-time placement updates
   - Player position synchronization
   - Chat system implementation
   - Conflict resolution strategies

### Phase 5: Client Refactoring
1. **Remove Service Worker**
   - Update client to make direct API calls
   - Remove FakeWebSocket implementation
   - Implement proper error handling
   - Add offline detection and graceful degradation

2. **Network Layer**
   - Create API client wrapper
   - Implement request retry logic
   - Add caching for frequently accessed data
   - Handle network timeouts and errors

## Key Considerations

### Service Worker Evaluation
**Recommendation: Remove service workers entirely**

Reasons:
- Service workers are primarily for offline functionality and request interception
- Real multiplayer requires server-side state management
- Direct API calls are simpler and more reliable
- WebSocket connections need to be persistent, not intercepted

### Backward Compatibility
- Maintain offline functionality during transition
- Implement progressive enhancement
- Provide fallback mechanisms for network failures
- Preserve existing save data

### Security Measures
- Input validation and sanitization
- Rate limiting for API endpoints
- CSRF protection
- SQL injection prevention (NoSQL injection for MongoDB)
- Secure WebSocket connections

### Performance Optimization
- Database indexing strategy
- Connection pooling
- Caching frequently accessed data
- Efficient data serialization
- Minimize network round trips

## Risk Mitigation

### Data Loss Prevention
- Comprehensive backup strategy
- Migration validation scripts
- Rollback procedures
- Data integrity checks

### Downtime Minimization
- Blue-green deployment strategy
- Feature flags for gradual rollout
- Health checks and monitoring
- Graceful degradation

### Testing Strategy
- Unit tests for all API endpoints
- Integration tests for data flow
- Load testing for multiplayer scenarios
- End-to-end testing for critical paths

## Success Metrics

- Zero data loss during migration
- API response times < 200ms for 95% of requests
- WebSocket connection stability > 99%
- Successful multiplayer sessions
- Backward compatibility maintained

## Next Steps

1. Begin with MongoDB schema design and setup
2. Create data migration scripts and validate with test data
3. Implement core API endpoints incrementally
4. Set up WebSocket server for real-time features
5. Refactor client code to remove service worker dependency
6. Comprehensive testing and validation
7. Production deployment with monitoring

This transition will transform Xnafuland from a single-player offline experience to a true multiplayer online game while preserving all existing functionality.
