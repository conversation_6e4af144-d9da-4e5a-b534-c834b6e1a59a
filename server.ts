import express from 'express';
import path from 'path';
import { MongoClient, Db } from 'mongodb';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import cors from 'cors';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import rateLimit from 'express-rate-limit';

const app = express();
const server = createServer(app);

// MongoDB Configuration
const uri = "mongodb://localhost:27017";
const dbName = "Xnafu"; // Match the database name from Storage2.ts

let db: Db;
let mongoClient: MongoClient;

// JWT Secret (in production, use environment variable)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(limiter);

// Connect to MongoDB
async function connectToMongoDB() {
  try {
    mongoClient = new MongoClient(uri);
    await mongoClient.connect();
    db = mongoClient.db(dbName);
    console.log("Connected to MongoDB successfully");

    // Initialize database schema and indexes
    const migrationUtils = new MigrationUtils(db);
    await migrationUtils.initializeSchema();

    // Setup development data if database is empty
    const playerCount = await db.collection('players').countDocuments();
    if (playerCount === 0) {
      console.log("Setting up development data...");
      await migrationUtils.setupDevelopmentData();
    }

  } catch (err) {
    console.error("Failed to connect to MongoDB", err);
    process.exit(1);
  }
}



// Authentication middleware
export function authenticateToken(req: any, res: any, next: any) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err: any, user: any) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid or expired token' });
    }
    req.user = user;
    next();
  });
}

// Import API routes and WebSocket server
import apiRoutes from './src/api/index';
import GameWebSocketServer from './src/websocket/gameSocket';
import { MigrationUtils } from './src/migration/migrationUtils';

// API Routes
app.use('/api', apiRoutes);

// Static Files and Routes
app.use(express.static(path.join(__dirname))); // Serve main offline files
app.use('/static', express.static(path.join(__dirname, 'static'))); // Serve static assets

// Homepage Routes
app.get(['/', '/index.html'], (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

app.get(['/xnafu-info.html', "/xnafu-info"], (req, res) => {
  res.sendFile(path.join(__dirname, 'xnafu-info.html'));
});

// Game Page Route
app.get('/game', (req, res) => {
  res.sendFile(path.join(__dirname, 'game.html'));
});

const PORT = process.env.PORT || 8081;

// Start server
async function startServer() {
  await connectToMongoDB();

  // Initialize WebSocket server
  const gameWS = new GameWebSocketServer(server);

  server.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
    console.log(`WebSocket server is running on ws://localhost:${PORT}/ws`);
  });
}

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down gracefully...');
  if (mongoClient) {
    await mongoClient.close();
  }
  process.exit(0);
});

startServer().catch(console.error);

export { app, db, server };