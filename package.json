{"dependencies": {"@types/qs": "^6.9.10", "axios": "^1.7.9", "body-parser": "^1.20.3", "daisyui": "latest", "express": "^4.21.1", "idb": "^7.1.1", "idb-keyval": "^6.2.1", "jszip": "^3.10.1", "mongodb": "^6.11.0", "mongoose": "^8.8.0", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "ws": "^8.18.0", "zod": "^3.22.4"}, "scripts": {"tsc": "tsc --project tsconfig.json", "tsc:w": "tsc --project tsconfig.json --watch", "scripts:w": "tsc --project jsconfig.scripts.json --watch", "tailwind": "tailwindcss -i ./_code/input.css -o ./static/offlineland/style.css", "tailwind:w": "tailwindcss -i ./_code/input.css -o ./static/offlineland/style.css --watch", "build_deprecated": "bun run tsc && bun run tailwind", "build": "tsc -b tsconfig.json tsconfig.server.json tsconfig.browser.json", "dev": "tsc -b tsconfig.json tsconfig.server.json tsconfig.browser.json --watch"}, "devDependencies": {"@types/express": "^5.0.0", "@types/file-saver": "^2.0.7", "bun-types": "^1.0.15", "file-saver": "^2.0.5"}}