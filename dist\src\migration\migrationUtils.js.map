{"version": 3, "file": "migrationUtils.js", "sourceRoot": "", "sources": ["../../../src/migration/migrationUtils.ts"], "names": [], "mappings": ";;;AAAA,qCAAuC;AACvC,+CAMuB;AAEvB,kEAAkE;AAElE,MAAa,cAAc;IAGzB,YAAY,QAAY;QACtB,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC;IACrB,CAAC;IAED,6CAA6C;IAC7C,KAAK,CAAC,gBAAgB;QACpB,MAAM,IAAA,gCAAkB,EAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpC,CAAC;IAED,kDAAkD;IAClD,KAAK,CAAC,mBAAmB;QACvB,MAAM,aAAa,GAAmB;YACpC,GAAG,EAAE,IAAI,kBAAQ,EAAE,CAAC,QAAQ,EAAE;YAC9B,UAAU,EAAE,aAAa;YACzB,QAAQ,EAAE,8BAA8B,EAAE,iCAAiC;YAC3E,aAAa,EAAE,IAAI;YACnB,YAAY,EAAE,IAAI;YAClB,QAAQ,EAAE,KAAK;YACf,IAAI,EAAE,EAAE;YACR,gBAAgB,EAAE,CAAC;YACnB,UAAU,EAAE,KAAK;YACjB,OAAO,EAAE,CAAC;YACV,mBAAmB,EAAE,IAAI;YACzB,UAAU,EAAE,GAAG;YACf,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC5E,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QAC1D,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;IACtC,CAAC;IAED,oCAAoC;IACpC,KAAK,CAAC,iBAAiB,CAAC,OAAe;QACrC,MAAM,WAAW,GAAiB;YAChC,GAAG,EAAE,IAAI,kBAAQ,EAAE,CAAC,QAAQ,EAAE;YAC9B,GAAG,EAAE,IAAI,kBAAQ,EAAE,CAAC,QAAQ,EAAE;YAC9B,GAAG,EAAE,KAAK;YACV,GAAG,EAAE,cAAc;YACnB,GAAG,EAAE,eAAe;YACpB,GAAG,EAAE,SAAS;YACd,GAAG,EAAE,mCAAmC;YACxC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACnB,GAAG,EAAE,OAAO;YACZ,GAAG,EAAE,KAAK;YACV,GAAG,EAAE,KAAK;YACV,GAAG,EAAE,KAAK;YACV,GAAG,EAAE,KAAK;YACV,GAAG,EAAE,CAAC;YACN,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC5E,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QACxD,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;IACtC,CAAC;IAED,qCAAqC;IACrC,KAAK,CAAC,oBAAoB,CAAC,MAAc;QACvC,MAAM,cAAc,GAAG;YACrB,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACd,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACd,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACd,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;SACf,CAAC;QAEF,KAAK,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,cAAc,EAAE,CAAC;YACtC,MAAM,MAAM,GAAmB;gBAC7B,MAAM;gBACN,CAAC;gBACD,CAAC;gBACD,CAAC,EAAE,IAAI;gBACP,GAAG,EAAE,EAAE;gBACP,EAAE,EAAE,EAAE;gBACN,CAAC,EAAE;oBACD,CAAC,EAAE,EAAE;oBACL,CAAC,EAAE,EAAE;oBACL,CAAC,EAAE,EAAE;oBACL,EAAE,EAAE,EAAE;iBACP;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,WAAW,cAAc,CAAC,MAAM,6BAA6B,MAAM,EAAE,CAAC,CAAC;IACrF,CAAC;IAED,4CAA4C;IAC5C,KAAK,CAAC,iBAAiB,CAAC,aAAkB;QACxC,MAAM,SAAS,GAAmB;YAChC,GAAG,EAAE,aAAa,CAAC,GAAG,IAAI,IAAI,kBAAQ,EAAE,CAAC,QAAQ,EAAE;YACnD,UAAU,EAAE,aAAa,CAAC,UAAU,IAAI,gBAAgB;YACxD,QAAQ,EAAE,8BAA8B,EAAE,uCAAuC;YACjF,aAAa,EAAE,aAAa,CAAC,aAAa,IAAI,IAAI;YAClD,YAAY,EAAE,aAAa,CAAC,YAAY,IAAI,IAAI;YAChD,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,KAAK;YACzC,IAAI,EAAE,aAAa,CAAC,IAAI,IAAI,CAAC;YAC7B,gBAAgB,EAAE,aAAa,CAAC,gBAAgB,IAAI,CAAC;YACrD,UAAU,EAAE,aAAa,CAAC,UAAU,IAAI,KAAK;YAC7C,OAAO,EAAE,aAAa,CAAC,OAAO,IAAI,CAAC;YACnC,mBAAmB,EAAE,aAAa,CAAC,mBAAmB,IAAI,IAAI;YAC9D,UAAU,EAAE,aAAa,CAAC,UAAU,IAAI,GAAG;YAC3C,cAAc,EAAE,aAAa,CAAC,cAAc;YAC5C,YAAY,EAAE,aAAa,CAAC,YAAY;YACxC,aAAa,EAAE,aAAa,CAAC,aAAa;YAC1C,aAAa,EAAE,aAAa,CAAC,aAAa;YAC1C,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QACxD,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;IACtC,CAAC;IAED,0CAA0C;IAC1C,KAAK,CAAC,eAAe,CAAC,WAAgB;QACpC,MAAM,OAAO,GAAiB;YAC5B,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,IAAI,kBAAQ,EAAE,CAAC,QAAQ,EAAE;YACjD,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,IAAI,kBAAQ,EAAE,CAAC,QAAQ,EAAE;YACjD,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,KAAK;YAC7B,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,cAAc;YACtC,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,eAAe;YACvC,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,SAAS;YACjC,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,gBAAgB;YACxC,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YACtC,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,0BAA0B;YAClD,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,KAAK;YAC7B,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,KAAK;YAC7B,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,KAAK;YAC7B,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,KAAK;YAC7B,GAAG,EAAE,WAAW,CAAC,GAAG,IAAI,CAAC;YACzB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QACtD,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;IACtC,CAAC;IAED,4CAA4C;IAC5C,KAAK,CAAC,iBAAiB,CAAC,aAAkB,EAAE,MAAc;QACxD,MAAM,SAAS,GAAmB;YAChC,MAAM;YACN,CAAC,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC;YACvB,CAAC,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC;YACvB,CAAC,EAAE,aAAa,CAAC,CAAC,IAAI,IAAI;YAC1B,GAAG,EAAE,aAAa,CAAC,GAAG,IAAI,EAAE;YAC5B,EAAE,EAAE,aAAa,CAAC,EAAE,IAAI,EAAE;YAC1B,CAAC,EAAE,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;YACrD,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC7E,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QACxD,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;IACtC,CAAC;IAED,8CAA8C;IAC9C,KAAK,CAAC,mBAAmB,CAAC,eAAoB;QAC5C,MAAM,WAAW,GAAqB;YACpC,IAAI,EAAE,eAAe,CAAC,IAAI,IAAI,kBAAkB;YAChD,IAAI,EAAE,eAAe,CAAC,IAAI,IAAI,EAAE;YAChC,OAAO,EAAE,eAAe,CAAC,OAAO,IAAI,0BAA0B;YAC9D,KAAK,EAAE,eAAe,CAAC,KAAK;YAC5B,SAAS,EAAE,eAAe,CAAC,SAAS;YACpC,KAAK,EAAE,eAAe,CAAC,KAAK,IAAI,CAAC;YACjC,WAAW,EAAE,eAAe,CAAC,WAAW,IAAI,CAAC;YAC7C,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC5E,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QAC1D,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;IACtC,CAAC;IAED,qCAAqC;IACrC,KAAK,CAAC,gBAAgB,CAAC,QAAgB,EAAE,KAAY;QACnD,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,CAAC;gBACH,IAAI,MAAc,CAAC;gBAEnB,QAAQ,QAAQ,EAAE,CAAC;oBACjB,KAAK,SAAS;wBACZ,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAC5C,MAAM;oBACR,KAAK,OAAO;wBACV,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;wBAC1C,MAAM;oBACR,KAAK,WAAW;wBACd,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;wBAC9C,MAAM;oBACR;wBACE,MAAM,IAAI,KAAK,CAAC,sBAAsB,QAAQ,EAAE,CAAC,CAAC;gBACtD,CAAC;gBAED,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,QAAQ,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC5D,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,kBAAkB,OAAO,CAAC,MAAM,IAAI,QAAQ,QAAQ,CAAC,CAAC;QAClE,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,+BAA+B;IAC/B,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC;YACH,MAAM,WAAW,GAAG;gBAClB,SAAS;gBACT,WAAW;gBACX,cAAc;gBACd,WAAW;aACZ,CAAC;YAEF,KAAK,MAAM,cAAc,IAAI,WAAW,EAAE,CAAC;gBACzC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,cAAc,EAAE,CAAC;gBACxE,OAAO,CAAC,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,YAAY,CAAC,CAAC;YACvD,CAAC;YAED,6BAA6B;YAC7B,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;YACpE,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,CAAC;YAEpE,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;YAEjD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,uCAAuC;IACvC,KAAK,CAAC,cAAc;QAClB,MAAM,WAAW,GAAG;YAClB,SAAS;YACT,WAAW;YACX,cAAc;YACd,WAAW;YACX,QAAQ;YACR,iBAAiB;YACjB,SAAS;YACT,gBAAgB;YAChB,uBAAuB;SACxB,CAAC;QAEF,KAAK,MAAM,cAAc,IAAI,WAAW,EAAE,CAAC;YACzC,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACxD,OAAO,CAAC,GAAG,CAAC,0BAA0B,cAAc,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,iDAAiD;IACjD,KAAK,CAAC,oBAAoB;QACxB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAE9C,wBAAwB;QACxB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAElD,sBAAsB;QACtB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAEtD,yBAAyB;QACzB,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAExC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAC/C,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;IAC9B,CAAC;CACF;AAzRD,wCAyRC"}