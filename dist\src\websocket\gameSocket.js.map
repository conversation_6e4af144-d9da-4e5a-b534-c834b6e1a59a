{"version": 3, "file": "gameSocket.js", "sourceRoot": "", "sources": ["../../../src/websocket/gameSocket.ts"], "names": [], "mappings": ";;;;;AASA,kCAEC;AAXD,2BAAgD;AAEhD,gEAA+B;AAC/B,qCAAuC;AAEvC,sCAAsC;AACtC,IAAI,EAAM,CAAC;AAEX,wCAAwC;AACxC,SAAgB,WAAW,CAAC,QAAY;IACtC,EAAE,GAAG,QAAQ,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,sCAAsC,CAAC;AAmBpF,MAAM,mBAAmB;IAKvB,YAAY,MAAc;QAHlB,YAAO,GAAwC,IAAI,GAAG,EAAE,CAAC;QACzD,gBAAW,GAA6B,IAAI,GAAG,EAAE,CAAC;QAGxD,IAAI,CAAC,GAAG,GAAG,IAAI,oBAAe,CAAC;YAC7B,MAAM;YACN,IAAI,EAAE,KAAK;SACZ,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9D,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,EAA0B,EAAE,OAAY;QACrE,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAExC,wBAAwB;QACxB,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,IAAY,EAAE,EAAE;YACtC,IAAI,CAAC;gBACH,MAAM,OAAO,GAAgB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAEzD,IAAI,OAAO,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC;oBACzB,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACxD,CAAC;qBAAM,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;oBACvB,MAAM,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;gBAC5C,CAAC;qBAAM,CAAC;oBACN,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,yBAAyB,CAAC,CAAC;gBAC5C,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;gBACjD,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAClB,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACvB,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,EAA0B,EAAE,KAAa;QACxE,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAQ,CAAC;YACrD,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC;gBACpD,GAAG,EAAE,IAAI,kBAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC;aACpC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;gBACjC,OAAO;YACT,CAAC;YAED,EAAE,CAAC,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;YACpC,EAAE,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;YAElC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YAElC,8BAA8B;YAC9B,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE;gBACnB,CAAC,EAAE,cAAc;gBACjB,IAAI,EAAE;oBACJ,QAAQ,EAAE,EAAE,CAAC,QAAQ;oBACrB,UAAU,EAAE,EAAE,CAAC,UAAU;iBAC1B;aACF,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,UAAU,8BAA8B,CAAC,CAAC;QAErE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,EAA0B,EAAE,OAAoB;QAC9E,QAAQ,OAAO,CAAC,CAAC,EAAE,CAAC;YAClB,KAAK,WAAW;gBACd,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC5C,MAAM;YAER,KAAK,YAAY;gBACf,MAAM,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC7C,MAAM;YAER,KAAK,aAAa;gBAChB,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM;YAER,KAAK,YAAY;gBACf,MAAM,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC7C,MAAM;YAER,KAAK,cAAc;gBACjB,MAAM,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC/C,MAAM;YAER,KAAK,cAAc;gBACjB,MAAM,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC/C,MAAM;YAER;gBACE,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,EAA0B,EAAE,IAAwB;QAC/E,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QAExB,6BAA6B;QAC7B,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,EAAE,CAAC,WAAW,GAAG,MAAM,CAAC;QAExB,sBAAsB;QACtB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAC1C,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC,GAAG,CAAC,EAAE,CAAC,QAAS,CAAC,CAAC;QAEhD,mCAAmC;QACnC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;YAC3B,CAAC,EAAE,eAAe;YAClB,IAAI,EAAE;gBACJ,QAAQ,EAAE,EAAE,CAAC,QAAQ;gBACrB,UAAU,EAAE,EAAE,CAAC,UAAU;aAC1B;SACF,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC;QAEhB,gDAAgD;QAChD,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;aAC/D,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC;aAChC,GAAG,CAAC,EAAE,CAAC,EAAE;YACR,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACpC,OAAO,MAAM,CAAC,CAAC,CAAC;gBACd,QAAQ,EAAE,EAAE;gBACZ,UAAU,EAAE,MAAM,CAAC,UAAU;aAC9B,CAAC,CAAC,CAAC,IAAI,CAAC;QACX,CAAC,CAAC;aACD,MAAM,CAAC,OAAO,CAAC,CAAC;QAEnB,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE;YACnB,CAAC,EAAE,YAAY;YACf,IAAI,EAAE;gBACJ,MAAM;gBACN,OAAO,EAAE,WAAW;aACrB;SACF,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,EAA0B,EAAE,IAAwB;QAChF,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;QAExB,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC,MAAM,CAAC,EAAE,CAAC,QAAS,CAAC,CAAC;YAEnD,sBAAsB;YACtB,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC7C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;YAC3B,CAAC,EAAE,aAAa;YAChB,IAAI,EAAE;gBACJ,QAAQ,EAAE,EAAE,CAAC,QAAQ;gBACrB,UAAU,EAAE,EAAE,CAAC,UAAU;aAC1B;SACF,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC;QAEhB,EAAE,CAAC,WAAW,GAAG,SAAS,CAAC;IAC7B,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,EAA0B,EAAE,IAAoB;QAC7E,IAAI,CAAC,EAAE,CAAC,WAAW;YAAE,OAAO;QAE5B,qCAAqC;QACrC,MAAM,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAC/C,EAAE,QAAQ,EAAE,EAAE,CAAC,QAAQ,EAAE,EACzB;YACE,IAAI,EAAE;gBACJ,GAAG,IAAI;gBACP,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB;SACF,EACD,EAAE,MAAM,EAAE,IAAI,EAAE,CACjB,CAAC;QAEF,yCAAyC;QACzC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE;YACnC,CAAC,EAAE,cAAc;YACjB,IAAI,EAAE;gBACJ,QAAQ,EAAE,EAAE,CAAC,QAAQ;gBACrB,GAAG,IAAI;aACR;SACF,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,EAA0B,EAAE,IAAS;QACjE,IAAI,CAAC,EAAE,CAAC,WAAW;YAAE,OAAO;QAE5B,mCAAmC;QACnC,0DAA0D;QAE1D,yCAAyC;QACzC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE;YACnC,CAAC,EAAE,aAAa;YAChB,IAAI,EAAE;gBACJ,QAAQ,EAAE,EAAE,CAAC,QAAQ;gBACrB,GAAG,IAAI;aACR;SACF,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,EAA0B,EAAE,IAAyB;QACnF,IAAI,CAAC,EAAE,CAAC,WAAW;YAAE,OAAO;QAE5B,MAAM,WAAW,GAAG;YAClB,QAAQ,EAAE,EAAE,CAAC,QAAQ;YACrB,UAAU,EAAE,EAAE,CAAC,UAAU;YACzB,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,gCAAgC;QAChC,MAAM,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,SAAS,CAAC;YAC7C,GAAG,WAAW;YACd,MAAM,EAAE,EAAE,CAAC,WAAW;SACvB,CAAC,CAAC;QAEH,yCAAyC;QACzC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW,EAAE;YACnC,CAAC,EAAE,cAAc;YACjB,IAAI,EAAE,WAAW;SAClB,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,EAA0B,EAAE,IAAS;QACnE,iDAAiD;QACjD,yDAAyD;QAEzD,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE;YACnB,CAAC,EAAE,eAAe;YAClB,IAAI,EAAE;YACJ,0BAA0B;aAC3B;SACF,CAAC,CAAC;IACL,CAAC;IAEO,mBAAmB,CAAC,EAA0B;QACpD,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;YAChB,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,CAAC,UAAU,eAAe,CAAC,CAAC;YAEpD,mBAAmB;YACnB,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;gBACnB,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;YACvD,CAAC;YAED,sBAAsB;YACtB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,EAA0B,EAAE,OAAoB;QAClE,IAAI,EAAE,CAAC,UAAU,KAAK,cAAS,CAAC,IAAI,EAAE,CAAC;YACrC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,MAAc,EAAE,OAAoB,EAAE,eAAwB;QACpF,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACnD,IAAI,CAAC,aAAa;YAAE,OAAO;QAE3B,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC/B,IAAI,QAAQ,KAAK,eAAe,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAC1C,IAAI,MAAM,EAAE,CAAC;oBACX,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,mBAAmB;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;IAC3B,CAAC;IAEM,kBAAkB,CAAC,MAAc;QACtC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;IACjD,CAAC;CACF;AAED,kBAAe,mBAAmB,CAAC"}