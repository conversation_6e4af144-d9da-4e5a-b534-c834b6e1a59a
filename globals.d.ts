// declare global {
//     var Qs: typeof import('qs');
//     var JSZip: typeof import('jszip/index.d.ts');
//     var Zod: typeof import('zod/lib/index.d.ts');
//     var idbKeyval: typeof import('idb-keyval/dist/index.d.ts');

//     var idb: typeof import('idb/build/index.d.ts');
//     type DBSchema = import('idb/build/index.d.ts').DBSchema;
//     type IDBPDatabase = import('idb/build/index.d.ts').IDBPDatabase;
//     type IDBPTransaction = import('idb/build/index.d.ts').IDBPTransaction;

//     var matchPath: (path: string) => (path: string) => boolean;

//     // Add LocalMLDatabase to the global scope
//     // var LocalMLDatabase: typeof import('./_code/service-worker/Storage copy').LocalMLDatabase;
// }

// export {};
declare global {
    var Qs: typeof import('qs');
    var express: typeof import('express');
    var bodyParser: typeof import('body-parser');
    var path: typeof import('path');
    var JSZip: typeof import('jszip/index.d.ts');
    var Zod: typeof import('zod/lib/index.d.ts');
    var idbKeyval: typeof import('idb-keyval/dist/index.d.ts');
    var idb: typeof import('idb/build/index.d.ts');
    type DBSchema = import('idb/build/index.d.ts').DBSchema;
    type IDBPDatabase = import('idb/build/index.d.ts').IDBPDatabase;
    type IDBPTransaction = import('idb/build/index.d.ts').IDBPTransaction;
    var matchPath: (path: string) => (path: string) => boolean;
    const { MongoClient } = require('mongodb');
    // var Db: typeof import('mongodb').Db;
    // type MongoClient = import('mongodb');
    type Db = import('mongodb');
    var ObjectId: typeof import('mongodb').ObjectId;
    // function saveCreation(player: any, itemData: any): Promise<string>;
    // Import everything from Storage copy and add to global scope
    // import * as StorageExports from './_code/service-worker/Storage copy';
    // import * as AllExports from './_code/service-worker/index';
    // // Directly add everything from StorageExports to the global scope
    // // This will add all exported items from Storage copy to the global scope directly
    // for (const key in StorageExports) {
    //     if (StorageExports.hasOwnProperty(key)) {
    //         globalThis[key] = StorageExports[key];
    //     }
    // }

    // for (const key in AllExports) {
    //     if (AllExports.hasOwnProperty(key)) {
    //         globalThis[key] = StorageExports[key];
    //     }
    // }

    // Optionally, you can add specific things here if needed
    // For example:
    // var LocalMLDatabase: typeof StorageExports.LocalMLDatabase;
}

export {};
