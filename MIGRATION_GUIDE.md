# Migration Guide: IndexedDB to MongoDB with Real APIs

This guide outlines the transition from the current offline architecture using IndexedDB and service workers to a fully online architecture with MongoDB and real APIs.

## Overview

### Before (Current Architecture)
- **Storage**: IndexedDB for local data storage
- **API**: Service worker intercepting `/j/` calls with FakeAPI
- **Multiplayer**: Simulated through service worker
- **Authentication**: Local player data in IndexedDB

### After (New Architecture)
- **Storage**: MongoDB database on server
- **API**: Real RESTful endpoints with Express.js
- **Multiplayer**: WebSocket-based real-time communication
- **Authentication**: JWT-based authentication with secure sessions

## Migration Steps

### 1. Backend Infrastructure ✅ COMPLETE
- [x] Set up Express.js server with MongoDB integration
- [x] Implement authentication middleware with JWT
- [x] Create WebSocket server for real-time features
- [x] Add rate limiting and security measures

### 2. Database Schema ✅ COMPLETE
- [x] Design MongoDB collections based on existing data models
- [x] Create proper indexes for performance
- [x] Implement migration utilities
- [x] Set up development data

### 3. API Endpoints ✅ COMPLETE
- [x] Authentication routes (`/api/auth/`)
- [x] Player management routes (`/api/player/`)
- [x] Game API routes (`/api/j/`) - matching original `/j/` pattern
- [x] WebSocket endpoints for real-time features

### 4. Client-Side Services ✅ COMPLETE
- [x] API service to replace service worker calls
- [x] WebSocket service for real-time features
- [x] Authentication management
- [x] Error handling and retry logic

### 5. Next Steps (TODO)
- [ ] Update client code to use new API service
- [ ] Remove service worker dependencies
- [ ] Implement proper error handling in UI
- [ ] Add loading states and offline detection
- [ ] Create comprehensive tests

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new player
- `POST /api/auth/login` - Login player
- `GET /api/auth/verify` - Verify JWT token

### Player Management
- `GET /api/player/profile` - Get player profile
- `PUT /api/player/profile` - Update player profile
- `GET /api/player/settings` - Get player settings
- `PUT /api/player/settings` - Update player settings
- `GET /api/player/inventory` - Get player inventory
- `GET /api/player/achievements` - Get player achievements

### Game API (matching original `/j/` pattern)
- `POST /api/j/i/` - Initialize area
- `GET /api/j/i/def/:creationId` - Get creation definition
- `POST /api/j/i/c/` - Create item
- `POST /api/j/m/s/` - Get map sectors
- `GET /api/j/m/sp/:x/:y/:ap/:aid` - Get sector plus (3x3 grid)
- `GET /api/j/u/fab/` - Get friends and blocked
- `POST /api/j/u/gfr/` - Get fresh rank
- `POST /api/j/u/a/` - Unlock achievement

### WebSocket Events
- `AUTH` - Authenticate WebSocket connection
- `JOIN_AREA` - Join an area for multiplayer
- `LEAVE_AREA` - Leave an area
- `PLAYER_MOVE` - Send player movement
- `PLACE_ITEM` - Place item in world
- `CHAT_MESSAGE` - Send chat message
- `SYNC_REQUEST` - Request game state sync

## Client Code Changes

### Replace Service Worker Calls
```typescript
// OLD: Service worker intercepted calls
fetch('/j/i/', { method: 'POST', body: JSON.stringify({ urlName }) })

// NEW: Direct API calls
import apiService from './_code/client/apiService';
const response = await apiService.initArea(urlName);
```

### WebSocket Integration
```typescript
// NEW: Real-time multiplayer
import websocketService from './_code/client/websocketService';

// Connect with authentication
await websocketService.connect(authToken);

// Join area for multiplayer
websocketService.joinArea(areaId);

// Send player movement
websocketService.sendPlayerMove({ x, y, areaId });
```

### Authentication Flow
```typescript
// NEW: Proper authentication
import apiService from './_code/client/apiService';

// Login
const response = await apiService.login(screenName, password);
if (response.success) {
  // Token is automatically stored
  console.log('Logged in successfully');
}

// Check if authenticated
if (apiService.isAuthenticated()) {
  // User is logged in
}
```

## Database Collections

### Core Collections
- `players` - Player accounts and profiles
- `area-data` - Area information and metadata
- `area-sectors` - Map sector data with placements
- `creations` - User-created items and objects
- `player-inventory` - Player inventories
- `player-achievements` - Player achievements
- `player-settings` - Player preferences
- `chat-messages` - Chat history
- `player-positions` - Real-time player positions

### Migration Collections
- `multis` - Multi-part creation data
- `motions-of-body` - Animation data
- `holders` - Container contents
- `creation-stats` - Creation usage statistics
- `creation-painter-data` - Painting tool data

## Security Improvements

### Authentication
- JWT tokens with expiration
- Bcrypt password hashing
- Rate limiting on auth endpoints
- Secure session management

### API Security
- Input validation with Zod schemas
- CORS configuration
- Request size limits
- Error message sanitization

### Database Security
- Proper indexing for performance
- Data validation at database level
- Connection pooling
- Graceful error handling

## Performance Optimizations

### Database
- Compound indexes for common queries
- Aggregation pipelines for complex data
- Connection pooling
- Query optimization

### API
- Response caching where appropriate
- Pagination for large datasets
- Compression for responses
- Rate limiting to prevent abuse

### WebSocket
- Connection management
- Message batching
- Automatic reconnection
- Heartbeat monitoring

## Testing Strategy

### Unit Tests
- API endpoint testing
- Database operation testing
- Authentication flow testing
- WebSocket message handling

### Integration Tests
- End-to-end API workflows
- Database migration testing
- Real-time multiplayer scenarios
- Error handling and recovery

### Performance Tests
- Load testing for concurrent users
- Database query performance
- WebSocket connection limits
- Memory usage monitoring

## Deployment Considerations

### Environment Variables
```env
NODE_ENV=production
PORT=8081
MONGODB_URI=mongodb://localhost:27017
JWT_SECRET=your-secure-secret-key
CORS_ORIGIN=https://yourdomain.com
```

### Production Setup
- MongoDB replica set for high availability
- Load balancer for multiple server instances
- SSL/TLS certificates for HTTPS
- Process manager (PM2) for server management
- Monitoring and logging setup

## Rollback Plan

If issues arise during migration:

1. **Immediate Rollback**: Revert to service worker architecture
2. **Data Backup**: Ensure MongoDB data is backed up before migration
3. **Gradual Migration**: Implement feature flags to enable new features gradually
4. **Monitoring**: Set up alerts for error rates and performance metrics

## Support and Troubleshooting

### Common Issues
- **Connection Errors**: Check MongoDB connection string and network
- **Authentication Failures**: Verify JWT secret and token expiration
- **WebSocket Issues**: Check firewall settings and proxy configuration
- **Performance Problems**: Review database indexes and query patterns

### Debugging Tools
- MongoDB Compass for database inspection
- Browser DevTools for API debugging
- WebSocket testing tools
- Server logs and monitoring

## Next Steps

1. **Test the Backend**: Start the server and test API endpoints
2. **Update Client Code**: Replace service worker calls with API service
3. **Implement Authentication**: Add login/register UI
4. **Add Real-time Features**: Integrate WebSocket service
5. **Remove Service Worker**: Clean up old service worker code
6. **Add Error Handling**: Implement proper error states in UI
7. **Performance Testing**: Test with multiple concurrent users
8. **Deploy to Production**: Set up production environment

This migration transforms your game from an offline-first architecture to a fully online multiplayer experience with proper authentication, real-time features, and scalable backend infrastructure.
