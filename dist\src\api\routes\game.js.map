{"version": 3, "file": "game.js", "sourceRoot": "", "sources": ["../../../../src/api/routes/game.ts"], "names": [], "mappings": ";;AAAA,qCAAiC;AACjC,6BAAwB;AACxB,qCAAmC;AACnC,yCAAkC;AAClC,yCAAiD;AAEjD,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,mCAAmC;AACnC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,0BAAiB,EAAE,KAAK,EAAE,GAAQ,EAAE,GAAG,EAAE,EAAE;IAC5D,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;YACtB,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;SACpB,CAAC,CAAC;QAEH,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE3C,0BAA0B;QAC1B,IAAI,QAAQ,GAAG,MAAM,WAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC;QAE1E,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,0CAA0C;YAC1C,QAAQ,GAAG;gBACT,GAAG,EAAE,IAAI,kBAAQ,EAAE,CAAC,QAAQ,EAAE;gBAC9B,GAAG,EAAE,IAAI,kBAAQ,EAAE,CAAC,QAAQ,EAAE;gBAC9B,GAAG,EAAE,KAAK;gBACV,GAAG,EAAE,OAAO;gBACZ,GAAG,EAAE,OAAO;gBACZ,GAAG,EAAE,OAAO;gBACZ,GAAG,EAAE,cAAc,OAAO,EAAE;gBAC5B,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;gBACnB,GAAG,EAAE,0BAA0B,EAAE,qBAAqB;gBACtD,GAAG,EAAE,KAAK;gBACV,GAAG,EAAE,KAAK;gBACV,GAAG,EAAE,KAAK;gBACV,GAAG,EAAE,KAAK;gBACV,GAAG,EAAE,CAAC;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,WAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QACvD,CAAC;QAED,uBAAuB;QACvB,MAAM,MAAM,GAAG,MAAM,WAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC;YACpD,GAAG,EAAE,IAAI,kBAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;SACrC,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE;gBACN,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAC1B,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;gBAClF,GAAG,EAAE,MAAM,CAAC,aAAa;gBACzB,GAAG,EAAE,MAAM,CAAC,mBAAmB;gBAC/B,GAAG,EAAE,MAAM,CAAC,QAAQ;gBACpB,GAAG,EAAE,MAAM,CAAC,UAAU;gBACtB,GAAG,EAAE,MAAM,CAAC,YAAY;gBACxB,GAAG,EAAE,EAAE,CAAC,kBAAkB;aAC3B;SACF,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAErB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,OAAC,CAAC,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACjF,CAAC;QACD,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QACzC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,qDAAqD;AACrD,MAAM,CAAC,GAAG,CAAC,oBAAoB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAClD,IAAI,CAAC;QACH,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAElC,IAAI,CAAC,kBAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAClC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,WAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC;YACxD,GAAG,EAAE,IAAI,kBAAQ,CAAC,UAAU,CAAC;SAC9B,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC,CAAC;QAC/D,CAAC;QAED,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAErB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,6BAA6B;AAC7B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,0BAAiB,EAAE,KAAK,EAAE,GAAQ,EAAE,GAAG,EAAE,EAAE;IAC9D,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;YACtB,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC;gBACjB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;gBAChB,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE;gBAChB,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;gBACnC,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;aACjC,CAAC;SACH,CAAC,CAAC;QAEH,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE5C,MAAM,QAAQ,GAAG;YACf,GAAG,QAAQ;YACX,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;YAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,KAAK,EAAE,CAAC;YACR,WAAW,EAAE,CAAC;SACf,CAAC;QAEF,MAAM,MAAM,GAAG,MAAM,WAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAEpE,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAErD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,OAAC,CAAC,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACjF,CAAC;QACD,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;QAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,iCAAiC;AACjC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,0BAAiB,EAAE,KAAK,EAAE,GAAQ,EAAE,GAAG,EAAE,EAAE;IAC9D,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;YACtB,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,oCAAoC;YACnD,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE,EAAE,UAAU;YACzB,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE,CAAE,YAAY;SAC5B,CAAC,CAAC;QAEH,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAEvC,MAAM,UAAU,GAAG,EAAE,CAAC;QAEtB,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,gBAAgB,EAAE,CAAC;YACtC,MAAM,MAAM,GAAG,MAAM,WAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC;gBACzD,MAAM,EAAE,CAAC;gBACT,CAAC,EAAE,CAAC;gBACJ,CAAC,EAAE,CAAC;aACL,CAAC,CAAC;YAEH,IAAI,MAAM,EAAE,CAAC;gBACX,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAEvB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,OAAC,CAAC,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACjF,CAAC;QACD,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC/C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,0DAA0D;AAC1D,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACpD,IAAI,CAAC;QACH,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QACrC,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QAE5B,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,4CAA4C;QAC5C,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;YAChC,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;gBAChC,MAAM,MAAM,GAAG,MAAM,WAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC;oBACzD,MAAM,EAAE,GAAG;oBACX,CAAC,EAAE,OAAO,GAAG,EAAE;oBACf,CAAC,EAAE,OAAO,GAAG,EAAE;iBAChB,CAAC,CAAC;gBAEH,IAAI,MAAM,EAAE,CAAC;oBACX,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;QACH,CAAC;QAED,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAEpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC/C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,sCAAsC;AACtC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,0BAAiB,EAAE,KAAK,EAAE,GAAQ,EAAE,GAAG,EAAE,EAAE;IAC/D,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,WAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC;YAChE,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;SAC5B,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,WAAW,EAAE,OAAO,IAAI,EAAE;YACnC,OAAO,EAAE,WAAW,EAAE,OAAO,IAAI,EAAE;SACpC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACvD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kCAAkC;AAClC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,0BAAiB,EAAE,KAAK,EAAE,GAAQ,EAAE,GAAG,EAAE,EAAE;IAChE,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,WAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC;YACpD,GAAG,EAAE,IAAI,kBAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC;SACrC,CAAC,CAAC;QAEH,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC;IAE9B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC9C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,6BAA6B;AAC7B,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,0BAAiB,EAAE,KAAK,EAAE,GAAQ,EAAE,GAAG,EAAE,EAAE;IAC9D,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,OAAC,CAAC,MAAM,CAAC;YACtB,EAAE,EAAE,OAAC,CAAC,MAAM,EAAE;SACf,CAAC,CAAC;QAEH,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAEtC,MAAM,WAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,SAAS,CAClD,EAAE,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,EAC/B;YACE,SAAS,EAAE;gBACT,YAAY,EAAE;oBACZ,EAAE;oBACF,UAAU,EAAE,IAAI,IAAI,EAAE;iBACvB;aACF;SACF,EACD,EAAE,MAAM,EAAE,IAAI,EAAE,CACjB,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;IAE9B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,OAAC,CAAC,QAAQ,EAAE,CAAC;YAChC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QACjF,CAAC;QACD,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;QAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC"}