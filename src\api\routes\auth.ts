import { Router, Request, Response } from 'express';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { z } from 'zod';
import { MongoClient, Db } from 'mongodb';

// We'll get the db instance passed in or create it here
let db: Db;

const router = Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Function to set the database instance
export function setDatabase(database: Db) {
  db = database;
}

// Validation schemas
const registerSchema = z.object({
  screenName: z.string().min(3).max(20),
  password: z.string().min(6),
  email: z.string().email().optional()
});

const loginSchema = z.object({
  screenName: z.string(),
  password: z.string()
});

// Register new player
router.post('/register', async (req: any, res: any) => {
  try {
    const { screenName, password, email } = registerSchema.parse(req.body);

    // Check if player already exists
    const existingPlayer = await db.collection('players').findOne({ screenName });
    if (existingPlayer) {
      return res.status(400).json({ error: 'Player name already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new player
    const newPlayer = {
      screenName,
      password: hashedPassword,
      email,
      isFullAccount: true,
      hasMinfinity: true,
      isBacker: false,
      rank: 1,
      stat_ItemsPlaced: 0,
      unfindable: false,
      ageDays: 0,
      leftMinfinityAmount: 1000,
      boostsLeft: 10,
      createdAt: new Date(),
      lastLogin: new Date()
    };

    const result = await db.collection('players').insertOne(newPlayer);
    const playerId = result.insertedId.toString();

    // Generate JWT token
    const token = jwt.sign(
      { playerId, screenName },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.status(201).json({
      success: true,
      token,
      player: {
        rid: playerId,
        screenName,
        isFullAccount: newPlayer.isFullAccount,
        rank: newPlayer.rank
      }
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Login player
router.post('/login', async (req: Request, res: Response) => {
  try {
    const { screenName, password } = loginSchema.parse(req.body);

    // Find player
    const player = await db.collection('players').findOne({ screenName });
    if (!player) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Verify password
    const validPassword = await bcrypt.compare(password, player.password);
    if (!validPassword) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Update last login
    await db.collection('players').updateOne(
      { _id: player._id },
      { $set: { lastLogin: new Date() } }
    );

    // Generate JWT token
    const token = jwt.sign(
      { playerId: player._id.toString(), screenName: player.screenName },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.json({
      success: true,
      token,
      player: {
        rid: player._id.toString(),
        screenName: player.screenName,
        isFullAccount: player.isFullAccount,
        rank: player.rank,
        hasMinfinity: player.hasMinfinity,
        isBacker: player.isBacker
      }
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Verify token endpoint
router.get('/verify', async (req: Request, res: Response) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    const player = await db.collection('players').findOne({ _id: decoded.playerId });

    if (!player) {
      return res.status(404).json({ error: 'Player not found' });
    }

    res.json({
      valid: true,
      player: {
        rid: player._id.toString(),
        screenName: player.screenName,
        isFullAccount: player.isFullAccount,
        rank: player.rank
      }
    });
  } catch (error) {
    res.status(403).json({ error: 'Invalid or expired token' });
  }
});

export default router;
