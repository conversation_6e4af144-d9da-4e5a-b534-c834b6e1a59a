const express = require('express');
const path = require('path');
const { MongoClient } = require('mongodb'); // Import MongoDB client

const app = express();

// MongoDB Configuration
const uri = "mongodb://localhost:27017"; // Replace with your MongoDB connection string
const dbName = "myDatabase"; // Replace with your database name

let db; // To store the database connection

// Connect to MongoDB
MongoClient.connect(uri, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(client => {
    console.log("Connected to MongoDB");
    db = client.db(dbName);
  })
  .catch(err => {
    console.error("Failed to connect to MongoDB", err);
  });

// Static Files and Routes
app.use(express.static(path.join(__dirname))); // Serve main offline files
app.use('/static', express.static(path.join(__dirname, 'static'))); // Serve static assets
app.use('/service-worker.js', express.static(path.join(__dirname, 'service-worker.js'))); // Service worker

// Homepage Routes
app.get(['/', '/index.html'], (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

app.get(['/xnafu-info.html', "/xnafu-info"], (req, res) => {
  res.sendFile(path.join(__dirname, 'xnafu-info.html'));
});

// Game Page Route
app.get('/game', (req, res) => {
  res.sendFile(path.join(__dirname, 'game.html'));
});

const PORT = 8081;
app.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});
