{
    "compilerOptions": {
        "strict": false,
        "target": "ESNext",
        "lib": [
            "ESNext",
            "webworker",
            "D<PERSON>"
        ],
        "checkJs": true,
        "skipLibCheck": true,
        "module": "None",
        "moduleResolution": "Classic",
        "isolatedModules": true,
        "baseUrl": ".",
        "paths": {
            "*": [
                "node_modules/*"
            ]
        }
    },
    "include": [
        "_code/service-worker/**/*.ts",
        "_code/client/**/*.ts",
        "globals.d.ts",
        "service-worker.ts"
    ],
    "exclude": [
        "_code/libs/*.js",
        "server.ts",
        "src/**/*"
    ]
}
// {
//     "compilerOptions": {
//         "strict": false,
//         "target": "ESNext",
//         "lib": [
//             "ESNext",
//             "DOM",
//             "DOM.Iterable",
//             "WebWorker"
//         ],
//         "skipLibCheck": true,
//         "module": "esnext",
//         "moduleResolution": "node",
//         "allowJs": true,
//         "esModuleInterop": true,
//         "resolveJsonModule": true,
//         "isolatedModules": false,
//         "baseUrl": ".",
//         "outDir": "./dist"
//     },
//     "include": [
//         "_code/service-worker/**/*",
//         "globals.d.ts",
//         "service-worker.ts"
//     ],
//     "exclude": [
//         "node_modules",
//         "dist"
//     ]
// }
// {
//     "compilerOptions": {
//         "strict": false,
//         "target": "ESNext",
//         "lib": [
//             "ESNext",
//             "DOM",
//             "DOM.Iterable",
//             "WebWorker"
//         ],
//         "skipLibCheck": true,
//         "moduleResolution": "node",
//         "allowJs": true,
//         "esModuleInterop": true,
//         "resolveJsonModule": true,
//         "isolatedModules": false,
//         "baseUrl": ".",
//         "composite": true,
//         "declaration": true,
//         "outDir": "./dist",
//         "rootDir": ".",
//         "paths": {
//             "*": ["node_modules/*"],
//             "@browser/*": ["_code/service-worker/*"],
//             "@server/*": ["./*"]
//         }
//     },
//     "include": [
//         "_code/service-worker/**/*.ts",
//         "globals.d.ts",
//         "service-worker.ts"
//     ],
//     "exclude": [
//         // "server.ts",
//         "node_modules",
//         "dist"
//     ]
// }