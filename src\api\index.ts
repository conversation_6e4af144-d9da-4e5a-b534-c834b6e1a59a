import { Router } from 'express';
import { Db } from 'mongodb';
import authRoutes, { setDatabase as setAuthDatabase } from './routes/auth';
import playerRoutes, { setDatabase as setPlayerDatabase } from './routes/player';
import gameRoutes, { setDatabase as setGameDatabase } from './routes/game';

const router = Router();

// Function to initialize routes with database
export function initializeRoutes(database: Db) {
  setAuthDatabase(database);
  setPlayerDatabase(database);
  setGameDatabase(database);
}

// API Routes
router.use('/auth', authRoutes);
router.use('/player', playerRoutes);

// Game API routes (matching the original /j/ pattern)
router.use('/j', gameRoutes);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Catch-all for unmatched API routes
router.use('*', (req, res) => {
  res.status(404).json({
    error: 'API endpoint not found',
    path: req.originalUrl,
    method: req.method
  });
});

export default router;
