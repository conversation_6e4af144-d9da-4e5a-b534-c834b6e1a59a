// Client-side WebSocket service for real-time multiplayer features
class WebSocketService {
    constructor(url = '') {
        this.ws = null;
        this.token = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.messageHandlers = new Map();
        this.isConnected = false;
        this.currentArea = null;
        this.url = url || `ws://${window.location.host}/ws`;
    }
    // Connection management
    connect(token) {
        return new Promise((resolve, reject) => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                resolve();
                return;
            }
            this.token = token;
            this.ws = new WebSocket(this.url);
            this.ws.onopen = () => {
                console.log('WebSocket connected');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                // Authenticate immediately after connection
                this.authenticate(token);
                resolve();
            };
            this.ws.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleMessage(message);
                }
                catch (error) {
                    console.error('Failed to parse WebSocket message:', error);
                }
            };
            this.ws.onclose = (event) => {
                console.log('WebSocket disconnected:', event.code, event.reason);
                this.isConnected = false;
                this.attemptReconnect();
            };
            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                reject(error);
            };
        });
    }
    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
            this.isConnected = false;
            this.currentArea = null;
        }
    }
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            return;
        }
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
        setTimeout(() => {
            if (this.token) {
                this.connect(this.token).catch(console.error);
            }
        }, delay);
    }
    // Authentication
    authenticate(token) {
        this.sendMessage({
            m: 'AUTH',
            data: { token }
        });
    }
    // Message handling
    handleMessage(message) {
        const handlers = this.messageHandlers.get(message.m) || [];
        handlers.forEach(handler => {
            try {
                handler(message);
            }
            catch (error) {
                console.error('Error in message handler:', error);
            }
        });
        // Handle built-in messages
        switch (message.m) {
            case 'AUTH_SUCCESS':
                console.log('WebSocket authentication successful');
                break;
            case 'PLAYER_JOINED':
                console.log('Player joined:', message.data);
                break;
            case 'PLAYER_LEFT':
                console.log('Player left:', message.data);
                break;
            case 'PLAYER_MOVED':
                this.handlePlayerMoved(message.data);
                break;
            case 'ITEM_PLACED':
                this.handleItemPlaced(message.data);
                break;
            case 'CHAT_MESSAGE':
                this.handleChatMessage(message.data);
                break;
        }
    }
    handlePlayerMoved(data) {
        // Emit custom event for game to handle
        window.dispatchEvent(new CustomEvent('playerMoved', { detail: data }));
    }
    handleItemPlaced(data) {
        // Emit custom event for game to handle
        window.dispatchEvent(new CustomEvent('itemPlaced', { detail: data }));
    }
    handleChatMessage(data) {
        // Emit custom event for chat system to handle
        window.dispatchEvent(new CustomEvent('chatMessage', { detail: data }));
    }
    // Message sending
    sendMessage(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
        else {
            console.warn('WebSocket not connected, message not sent:', message);
        }
    }
    // Event subscription
    on(messageType, handler) {
        if (!this.messageHandlers.has(messageType)) {
            this.messageHandlers.set(messageType, []);
        }
        this.messageHandlers.get(messageType).push(handler);
    }
    off(messageType, handler) {
        const handlers = this.messageHandlers.get(messageType);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }
    // Game actions
    joinArea(areaId) {
        this.currentArea = areaId;
        this.sendMessage({
            m: 'JOIN_AREA',
            data: { areaId }
        });
    }
    leaveArea(areaId) {
        this.sendMessage({
            m: 'LEAVE_AREA',
            data: { areaId }
        });
        this.currentArea = null;
    }
    sendPlayerMove(position) {
        this.sendMessage({
            m: 'PLAYER_MOVE',
            data: position
        });
    }
    sendPlaceItem(itemData) {
        this.sendMessage({
            m: 'PLACE_ITEM',
            data: itemData
        });
    }
    sendChatMessage(message) {
        if (!this.currentArea) {
            console.warn('Cannot send chat message: not in an area');
            return;
        }
        this.sendMessage({
            m: 'CHAT_MESSAGE',
            data: { message }
        });
    }
    requestSync(data = {}) {
        this.sendMessage({
            m: 'SYNC_REQUEST',
            data
        });
    }
    // Utility methods
    isConnectedToServer() {
        return this.isConnected;
    }
    getCurrentArea() {
        return this.currentArea;
    }
    // Helper method to set up common event listeners
    setupGameEventListeners() {
        // Listen for player movement from the game
        window.addEventListener('gamePlayerMove', (event) => {
            this.sendPlayerMove(event.detail);
        });
        // Listen for item placement from the game
        window.addEventListener('gameItemPlace', (event) => {
            this.sendPlaceItem(event.detail);
        });
        // Listen for chat messages from the UI
        window.addEventListener('gameChatSend', (event) => {
            this.sendChatMessage(event.detail.message);
        });
    }
    // Clean up event listeners
    removeGameEventListeners() {
        // Note: These would need to be stored as bound functions to properly remove
        // For now, just log that cleanup is needed
        console.log('Game event listeners cleanup needed');
    }
}
// Create a singleton instance
const websocketService = new WebSocketService();
// Export for use in other modules
export default websocketService;
// Also export the class for testing or multiple instances
export { WebSocketService };
