"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_1 = __importDefault(require("./routes/auth"));
const player_1 = __importDefault(require("./routes/player"));
const game_1 = __importDefault(require("./routes/game"));
const router = (0, express_1.Router)();
// API Routes
router.use('/auth', auth_1.default);
router.use('/player', player_1.default);
// Game API routes (matching the original /j/ pattern)
router.use('/j', game_1.default);
// Health check endpoint
router.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
    });
});
// Catch-all for unmatched API routes
router.use('*', (req, res) => {
    res.status(404).json({
        error: 'API endpoint not found',
        path: req.originalUrl,
        method: req.method
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map