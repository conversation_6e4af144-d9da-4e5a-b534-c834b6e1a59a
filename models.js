// models.js
const mongoose = require('mongoose');

const areaDataSchema = new mongoose.Schema({
    aid: String,
    gid: String,
    sub: Boolean,
    arn: String,
    agn: String,
    aun: String,
    ard: String,
    acl: { x: Number, y: Number },
    iid: String,
    adr: { angle: Number, speed: Number },
    apr: String,
    axx: <PERSON><PERSON>an,
    aul: <PERSON>olean,
    spe: <PERSON><PERSON><PERSON>,
    ece: Boolean,
    mpv: Number,
});

const sectorDataSchema = new mongoose.Schema({
    iix: [String],
    ps: [[Number, Number, Number, Number, Number, Number, String]],
    i: {
        b: [String],
        p: [{}],
        n: [String],
        dr: [Number],
    },
    v: Number,
    x: Number,
    y: Number,
});

const AreaData = mongoose.model('AreaData', areaDataSchema);
const SectorData = mongoose.model('SectorData', sectorDataSchema);

module.exports = { AreaData, SectorData };