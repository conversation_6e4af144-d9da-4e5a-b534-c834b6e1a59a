{"version": 3, "file": "mongoSchema.js", "sourceRoot": "", "sources": ["../../../src/migration/mongoSchema.ts"], "names": [], "mappings": ";;AAoMA,gDAwCC;AAzCD,2CAA2C;AACpC,KAAK,UAAU,kBAAkB,CAAC,EAAM;IAC7C,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;IAE/D,yCAAyC;IACzC,MAAM,WAAW,GAAG;QAClB,SAAS;QACT,WAAW;QACX,cAAc;QACd,WAAW;QACX,QAAQ;QACR,iBAAiB;QACjB,SAAS;QACT,gBAAgB;QAChB,uBAAuB;QACvB,kBAAkB;QAClB,qBAAqB;QACrB,iBAAiB;QACjB,gBAAgB;QAChB,kBAAkB;QAClB,eAAe;QACf,sBAAsB;QACtB,gBAAgB;QAChB,uBAAuB;KACxB,CAAC;IAEF,KAAK,MAAM,cAAc,IAAI,WAAW,EAAE,CAAC;QACzC,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,uBAAuB,cAAc,EAAE,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC,CAAC,4BAA4B;gBACnD,OAAO,CAAC,KAAK,CAAC,6BAA6B,cAAc,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;IACH,CAAC;IAED,wCAAwC;IACxC,MAAM,aAAa,CAAC,EAAE,CAAC,CAAC;IAExB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;AAClD,CAAC;AAED,KAAK,UAAU,aAAa,CAAC,EAAM;IACjC,IAAI,CAAC;QACH,iBAAiB;QACjB,MAAM,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAC3E,MAAM,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAClF,MAAM,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAE7E,eAAe;QACf,MAAM,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAC7E,MAAM,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;QAC3D,MAAM,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAC7E,MAAM,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;QAE3D,iBAAiB;QACjB,MAAM,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QACnG,MAAM,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;QAEjE,mBAAmB;QACnB,MAAM,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;QAC/D,MAAM,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClE,MAAM,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAE9D,gBAAgB;QAChB,MAAM,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC,CAAC;QAE/D,iBAAiB;QACjB,MAAM,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,WAAW,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAEtF,iBAAiB;QACjB,MAAM,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAEhF,gBAAgB;QAChB,MAAM,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,WAAW,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAEzF,uBAAuB;QACvB,MAAM,EAAE,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC,WAAW,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAEhG,yBAAyB;QACzB,MAAM,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QACzF,MAAM,EAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAC5F,MAAM,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QACxF,MAAM,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QACvF,MAAM,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QACzF,MAAM,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,WAAW,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;QAErE,eAAe;QACf,MAAM,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACnF,MAAM,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;QAEpE,wBAAwB;QACxB,MAAM,EAAE,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAE7F,kBAAkB;QAClB,MAAM,EAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC,WAAW,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;QACnE,MAAM,EAAE,CAAC,UAAU,CAAC,uBAAuB,CAAC,CAAC,WAAW,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;QAE1E,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACvD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC;AACH,CAAC"}