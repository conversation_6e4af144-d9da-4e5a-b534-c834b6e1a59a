name: Build and Deploy

permissions:
  contents: write

on:
  push:
    branches:
      - main

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - uses: oven-sh/setup-bun@v1

    - run: bun install
    - run: bun run build


    - name: Commit and Push to Build Branch
      run: |
        git config --global user.email "<EMAIL>"
        git config --global user.name "The little robot inside Github.com"
        git checkout -b build
        git add -f --all
        git commit -m "Build files"
        git push origin build --force

