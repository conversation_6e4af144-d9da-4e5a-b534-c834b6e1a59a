// Client-side WebSocket service for real-time multiplayer features

interface GameMessage {
  m: string;
  data: any;
}

interface PlayerPosition {
  x: number;
  y: number;
  areaId: string;
}

interface ChatMessage {
  playerId: string;
  playerName: string;
  message: string;
  timestamp: Date;
}

type MessageHandler = (message: GameMessage) => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private url: string;
  private token: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private messageHandlers: Map<string, MessageHandler[]> = new Map();
  private isConnected = false;
  private currentArea: string | null = null;

  constructor(url: string = '') {
    this.url = url || `ws://${window.location.host}/ws`;
  }

  // Connection management
  connect(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      this.token = token;
      this.ws = new WebSocket(this.url);

      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;

        // Authenticate immediately after connection
        this.authenticate(token);
        resolve();
      };

      this.ws.onmessage = (event) => {
        try {
          const message: GameMessage = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        this.isConnected = false;
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        reject(error);
      };
    });
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
      this.isConnected = false;
      this.currentArea = null;
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);

    setTimeout(() => {
      if (this.token) {
        this.connect(this.token).catch(console.error);
      }
    }, delay);
  }

  // Authentication
  private authenticate(token: string) {
    this.sendMessage({
      m: 'AUTH',
      data: { token }
    });
  }

  // Message handling
  private handleMessage(message: GameMessage) {
    const handlers = this.messageHandlers.get(message.m) || [];
    handlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('Error in message handler:', error);
      }
    });

    // Handle built-in messages
    switch (message.m) {
      case 'AUTH_SUCCESS':
        console.log('WebSocket authentication successful');
        break;
      case 'PLAYER_JOINED':
        console.log('Player joined:', message.data);
        break;
      case 'PLAYER_LEFT':
        console.log('Player left:', message.data);
        break;
      case 'PLAYER_MOVED':
        this.handlePlayerMoved(message.data);
        break;
      case 'ITEM_PLACED':
        this.handleItemPlaced(message.data);
        break;
      case 'CHAT_MESSAGE':
        this.handleChatMessage(message.data);
        break;
    }
  }

  private handlePlayerMoved(data: PlayerPosition & { playerId: string }) {
    // Emit custom event for game to handle
    window.dispatchEvent(new CustomEvent('playerMoved', { detail: data }));
  }

  private handleItemPlaced(data: any) {
    // Emit custom event for game to handle
    window.dispatchEvent(new CustomEvent('itemPlaced', { detail: data }));
  }

  private handleChatMessage(data: ChatMessage) {
    // Emit custom event for chat system to handle
    window.dispatchEvent(new CustomEvent('chatMessage', { detail: data }));
  }

  // Message sending
  private sendMessage(message: GameMessage) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, message not sent:', message);
    }
  }

  // Event subscription
  on(messageType: string, handler: MessageHandler) {
    if (!this.messageHandlers.has(messageType)) {
      this.messageHandlers.set(messageType, []);
    }
    this.messageHandlers.get(messageType)!.push(handler);
  }

  off(messageType: string, handler: MessageHandler) {
    const handlers = this.messageHandlers.get(messageType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // Game actions
  joinArea(areaId: string) {
    this.currentArea = areaId;
    this.sendMessage({
      m: 'JOIN_AREA',
      data: { areaId }
    });
  }

  leaveArea(areaId: string) {
    this.sendMessage({
      m: 'LEAVE_AREA',
      data: { areaId }
    });
    this.currentArea = null;
  }

  sendPlayerMove(position: PlayerPosition) {
    this.sendMessage({
      m: 'PLAYER_MOVE',
      data: position
    });
  }

  sendPlaceItem(itemData: any) {
    this.sendMessage({
      m: 'PLACE_ITEM',
      data: itemData
    });
  }

  sendChatMessage(message: string) {
    if (!this.currentArea) {
      console.warn('Cannot send chat message: not in an area');
      return;
    }

    this.sendMessage({
      m: 'CHAT_MESSAGE',
      data: { message }
    });
  }

  requestSync(data: any = {}) {
    this.sendMessage({
      m: 'SYNC_REQUEST',
      data
    });
  }

  // Utility methods
  isConnectedToServer(): boolean {
    return this.isConnected;
  }

  getCurrentArea(): string | null {
    return this.currentArea;
  }

  // Helper method to set up common event listeners
  setupGameEventListeners() {
    // Listen for player movement from the game
    window.addEventListener('gamePlayerMove', (event: any) => {
      this.sendPlayerMove(event.detail);
    });

    // Listen for item placement from the game
    window.addEventListener('gameItemPlace', (event: any) => {
      this.sendPlaceItem(event.detail);
    });

    // Listen for chat messages from the UI
    window.addEventListener('gameChatSend', (event: any) => {
      this.sendChatMessage(event.detail.message);
    });
  }

  // Clean up event listeners
  removeGameEventListeners() {
    // Note: These would need to be stored as bound functions to properly remove
    // For now, just log that cleanup is needed
    console.log('Game event listeners cleanup needed');
  }
}

// Create a singleton instance
const websocketService = new WebSocketService();

// Export for use in other modules
export default websocketService;

// Also export the class for testing or multiple instances
export { WebSocketService };

// Types for better TypeScript support
export type { GameMessage, PlayerPosition, ChatMessage, MessageHandler };
