"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const ws_1 = require("ws");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const server_1 = require("../server");
const mongodb_1 = require("mongodb");
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
class GameWebSocketServer {
    constructor(server) {
        this.clients = new Map();
        this.areaClients = new Map();
        this.wss = new ws_1.WebSocketServer({
            server,
            path: '/ws'
        });
        this.wss.on('connection', this.handleConnection.bind(this));
    }
    async handleConnection(ws, request) {
        console.log('New WebSocket connection');
        // Handle authentication
        ws.on('message', async (data) => {
            try {
                const message = JSON.parse(data.toString());
                if (message.m === 'AUTH') {
                    await this.authenticateClient(ws, message.data.token);
                }
                else if (ws.playerId) {
                    await this.handleGameMessage(ws, message);
                }
                else {
                    ws.close(1008, 'Authentication required');
                }
            }
            catch (error) {
                console.error('WebSocket message error:', error);
                ws.close(1011, 'Internal server error');
            }
        });
        ws.on('close', () => {
            this.handleDisconnection(ws);
        });
        ws.on('error', (error) => {
            console.error('WebSocket error:', error);
        });
    }
    async authenticateClient(ws, token) {
        try {
            const decoded = jsonwebtoken_1.default.verify(token, JWT_SECRET);
            const player = await server_1.db.collection('players').findOne({
                _id: new mongodb_1.ObjectId(decoded.playerId)
            });
            if (!player) {
                ws.close(1008, 'Invalid player');
                return;
            }
            ws.playerId = player._id.toString();
            ws.playerName = player.screenName;
            this.clients.set(ws.playerId, ws);
            // Send authentication success
            this.sendMessage(ws, {
                m: 'AUTH_SUCCESS',
                data: {
                    playerId: ws.playerId,
                    playerName: ws.playerName
                }
            });
            console.log(`Player ${ws.playerName} authenticated via WebSocket`);
        }
        catch (error) {
            console.error('WebSocket authentication error:', error);
            ws.close(1008, 'Authentication failed');
        }
    }
    async handleGameMessage(ws, message) {
        switch (message.m) {
            case 'JOIN_AREA':
                await this.handleJoinArea(ws, message.data);
                break;
            case 'LEAVE_AREA':
                await this.handleLeaveArea(ws, message.data);
                break;
            case 'PLAYER_MOVE':
                await this.handlePlayerMove(ws, message.data);
                break;
            case 'PLACE_ITEM':
                await this.handlePlaceItem(ws, message.data);
                break;
            case 'CHAT_MESSAGE':
                await this.handleChatMessage(ws, message.data);
                break;
            case 'SYNC_REQUEST':
                await this.handleSyncRequest(ws, message.data);
                break;
            default:
                console.log('Unknown message type:', message.m);
        }
    }
    async handleJoinArea(ws, data) {
        const { areaId } = data;
        // Leave previous area if any
        if (ws.currentArea) {
            await this.handleLeaveArea(ws, { areaId: ws.currentArea });
        }
        ws.currentArea = areaId;
        // Add to area clients
        if (!this.areaClients.has(areaId)) {
            this.areaClients.set(areaId, new Set());
        }
        this.areaClients.get(areaId).add(ws.playerId);
        // Notify other players in the area
        this.broadcastToArea(areaId, {
            m: 'PLAYER_JOINED',
            data: {
                playerId: ws.playerId,
                playerName: ws.playerName
            }
        }, ws.playerId);
        // Send current area state to the joining player
        const areaPlayers = Array.from(this.areaClients.get(areaId) || [])
            .filter(id => id !== ws.playerId)
            .map(id => {
            const client = this.clients.get(id);
            return client ? {
                playerId: id,
                playerName: client.playerName
            } : null;
        })
            .filter(Boolean);
        this.sendMessage(ws, {
            m: 'AREA_STATE',
            data: {
                areaId,
                players: areaPlayers
            }
        });
    }
    async handleLeaveArea(ws, data) {
        const { areaId } = data;
        if (this.areaClients.has(areaId)) {
            this.areaClients.get(areaId).delete(ws.playerId);
            // Clean up empty area
            if (this.areaClients.get(areaId).size === 0) {
                this.areaClients.delete(areaId);
            }
        }
        // Notify other players
        this.broadcastToArea(areaId, {
            m: 'PLAYER_LEFT',
            data: {
                playerId: ws.playerId,
                playerName: ws.playerName
            }
        }, ws.playerId);
        ws.currentArea = undefined;
    }
    async handlePlayerMove(ws, data) {
        if (!ws.currentArea)
            return;
        // Update player position in database
        await server_1.db.collection('player-positions').updateOne({ playerId: ws.playerId }, {
            $set: {
                ...data,
                updatedAt: new Date()
            }
        }, { upsert: true });
        // Broadcast to other players in the area
        this.broadcastToArea(ws.currentArea, {
            m: 'PLAYER_MOVED',
            data: {
                playerId: ws.playerId,
                ...data
            }
        }, ws.playerId);
    }
    async handlePlaceItem(ws, data) {
        if (!ws.currentArea)
            return;
        // Validate and save item placement
        // This would integrate with your existing placement logic
        // Broadcast to other players in the area
        this.broadcastToArea(ws.currentArea, {
            m: 'ITEM_PLACED',
            data: {
                playerId: ws.playerId,
                ...data
            }
        }, ws.playerId);
    }
    async handleChatMessage(ws, data) {
        if (!ws.currentArea)
            return;
        const chatMessage = {
            playerId: ws.playerId,
            playerName: ws.playerName,
            message: data.message,
            timestamp: new Date()
        };
        // Save chat message to database
        await server_1.db.collection('chat-messages').insertOne({
            ...chatMessage,
            areaId: ws.currentArea
        });
        // Broadcast to other players in the area
        this.broadcastToArea(ws.currentArea, {
            m: 'CHAT_MESSAGE',
            data: chatMessage
        });
    }
    async handleSyncRequest(ws, data) {
        // Handle synchronization requests for game state
        // This would be used for things like moving blocks, etc.
        this.sendMessage(ws, {
            m: 'SYNC_RESPONSE',
            data: {
            // Sync data would go here
            }
        });
    }
    handleDisconnection(ws) {
        if (ws.playerId) {
            console.log(`Player ${ws.playerName} disconnected`);
            // Remove from area
            if (ws.currentArea) {
                this.handleLeaveArea(ws, { areaId: ws.currentArea });
            }
            // Remove from clients
            this.clients.delete(ws.playerId);
        }
    }
    sendMessage(ws, message) {
        if (ws.readyState === ws_1.WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
        }
    }
    broadcastToArea(areaId, message, excludePlayerId) {
        const areaPlayerIds = this.areaClients.get(areaId);
        if (!areaPlayerIds)
            return;
        areaPlayerIds.forEach(playerId => {
            if (playerId !== excludePlayerId) {
                const client = this.clients.get(playerId);
                if (client) {
                    this.sendMessage(client, message);
                }
            }
        });
    }
    getConnectedPlayers() {
        return this.clients.size;
    }
    getAreaPlayerCount(areaId) {
        return this.areaClients.get(areaId)?.size || 0;
    }
}
exports.default = GameWebSocketServer;
//# sourceMappingURL=gameSocket.js.map