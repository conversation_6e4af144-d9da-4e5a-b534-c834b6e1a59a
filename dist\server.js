"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.server = exports.db = exports.app = void 0;
exports.authenticateToken = authenticateToken;
const express_1 = __importDefault(require("express"));
const path_1 = __importDefault(require("path"));
const mongodb_1 = require("mongodb");
const http_1 = require("http");
const cors_1 = __importDefault(require("cors"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const app = (0, express_1.default)();
exports.app = app;
const server = (0, http_1.createServer)(app);
exports.server = server;
// MongoDB Configuration
const uri = "mongodb://localhost:27017";
const dbName = "Xnafu"; // Match the database name from Storage2.ts
let db;
let mongoClient;
// JWT Secret (in production, use environment variable)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
// Rate limiting
const limiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.'
});
// Middleware
app.use((0, cors_1.default)());
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true }));
app.use(limiter);
// Connect to MongoDB
async function connectToMongoDB() {
    try {
        mongoClient = new mongodb_1.MongoClient(uri);
        await mongoClient.connect();
        exports.db = db = mongoClient.db(dbName);
        console.log("Connected to MongoDB successfully");
        // Initialize database schema and indexes
        const migrationUtils = new migrationUtils_1.MigrationUtils(db);
        await migrationUtils.initializeSchema();
        // Setup development data if database is empty
        const playerCount = await db.collection('players').countDocuments();
        if (playerCount === 0) {
            console.log("Setting up development data...");
            await migrationUtils.setupDevelopmentData();
        }
    }
    catch (err) {
        console.error("Failed to connect to MongoDB", err);
        process.exit(1);
    }
}
// Authentication middleware
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }
    jsonwebtoken_1.default.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
}
// Import API routes and WebSocket server
const index_1 = __importDefault(require("./src/api/index"));
const gameSocket_1 = __importDefault(require("./src/websocket/gameSocket"));
const migrationUtils_1 = require("./src/migration/migrationUtils");
// API Routes
app.use('/api', index_1.default);
// Static Files and Routes
app.use(express_1.default.static(path_1.default.join(__dirname))); // Serve main offline files
app.use('/static', express_1.default.static(path_1.default.join(__dirname, 'static'))); // Serve static assets
// Homepage Routes
app.get(['/', '/index.html'], (req, res) => {
    res.sendFile(path_1.default.join(__dirname, 'index.html'));
});
app.get(['/xnafu-info.html', "/xnafu-info"], (req, res) => {
    res.sendFile(path_1.default.join(__dirname, 'xnafu-info.html'));
});
// Game Page Route
app.get('/game', (req, res) => {
    res.sendFile(path_1.default.join(__dirname, 'game.html'));
});
const PORT = process.env.PORT || 8081;
// Start server
async function startServer() {
    await connectToMongoDB();
    // Initialize WebSocket server
    const gameWS = new gameSocket_1.default(server);
    server.listen(PORT, () => {
        console.log(`Server is running on http://localhost:${PORT}`);
        console.log(`WebSocket server is running on ws://localhost:${PORT}/ws`);
    });
}
// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down gracefully...');
    if (mongoClient) {
        await mongoClient.close();
    }
    process.exit(0);
});
startServer().catch(console.error);
//# sourceMappingURL=server.js.map