<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/png" href="/static/offlineland/favicons/unplugged-1-grey.png">
    <title>Xnafu X-plorer</title>
    <meta name="description" content="An interactive monument to Manyland">
    <meta property="og:image" content="/static/media/default_thumb.png">
    <meta property="og:url" content="https://offlineland.io">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Offlineland.io">
    <meta name="twitter:description" content="An interactive monument to Manyland">
    <meta name="twitter:image" content="/static/media/default_thumb.png">
    <link rel="manifest" href="/manifest.json" />
    <link href="/static/offlineland/style.css" rel="stylesheet" />
    <link href="/_code/libs/filepond.min.css" rel="stylesheet" />
    <link href="/_code/libs/toastify.css" rel="stylesheet" />
    <script src="/_code/libs/redom.js"></script>
    <script src="/_code/libs/zod.umd.js"></script>
    <script type="module" src="/_code/client/apiService.js"></script>
    <script type="module" src="/_code/client/websocketService.js"></script>
    <script type="module" src="/_code/mainscreen.mjs"></script>
</head>

<body>
    <!-- TODO find a way to not have to copy-paste this on every page lol -->
    <nav class="navbar">
        <div class="navbar-start">
            <a href="/" class="btn btn-ghost gap-1 h-2 min-h-[2rem] px-2 mx-1">
                <img style="image-rendering:pixelated" src="/static/offlineland/favicons/unplugged-1-grey.png"
                    class="w-6 h-6" />
                <span class="font-title text-base-content text-lg md:text-xl">Xnafu X-plorer</span>
            </a>
        </div>

        <div class="navbar-center">
            <div class="dropdown">
                <div tabindex="0" role="button" class="btn btn-ghost lg:hidden">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h8m-8 6h16" />
                    </svg>

                    <ul tabindex="0"
                        class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                        <li>
                            <details class="">
                                <summary class="py-1">
                                    Museum
                                </summary>
                                <ul class="p-2 bg-base-100 rounded-t-none">
                                    <li class="disabled"><a>
                                            Maps
                                            <span class="badge badge-sm badge-primary">Soon!</span>
                                        </a></li>
                                    <li class="disabled"><a>
                                            Sounds
                                            <span class="badge badge-sm badge-primary">Soon!</span>
                                        </a></li>
                                    <li class="disabled"><a>
                                            Universe Search
                                            <span class="badge badge-sm badge-primary">Soon!</span>
                                        </a></li>
                                </ul>
                            </details>
                        </li>
                        <li class="disabled"><a class="py-1">
                                Achievements
                                <span class="badge badge-sm badge-primary">Soon!</span>
                            </a></li>
                        <li><a class="py-1" href="/exporter">Xporter</a></li>
                        <!-- Replace "Onlineland" link with Login button -->
                        <li><a href="/login" class="btn btn-primary py-1">Login</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="navbar-end hidden lg:flex items-center">
            <ul class="menu menu-horizontal px-1 items-center">
                <li>
                    <details class="">
                        <summary class="py-1">
                            Museum
                        </summary>
                        <ul class="p-2 bg-base-100 rounded-t-none">
                            <li class="disabled"><a>Maps<span class="badge badge-sm badge-primary">Soon!</span></a></li>
                            <li class="disabled"><a>Sounds<span class="badge badge-sm badge-primary">Soon!</span></a>
                            </li>
                            <li class="disabled"><a>Universe Search<span
                                        class="badge badge-sm badge-primary">Soon!</span></a></li>
                        </ul>
                    </details>
                </li>
                <li class="disabled"><a class="py-1">Achievements<span
                            class="badge badge-sm badge-primary">Soon!</span></a></li>
                <li><a class="py-1" href="/exporter">Xporter</a></li>
                <!-- Adjusted Login button -->
                <li><a href="/login" class="btn btn-primary py-1 ml-2">Login</a></li>
            </ul>
        </div>

    </nav>



    <div class="">
        <div class="text-center pb-8">
            <div class="flex justify-center">
                <img src="/static/offlineland/favicons/unplugged-1-grey.png" class="w-10 h:10 md:w-20 md:h-20" />
                <h1 class="text-xl md:text-6xl font-bold p-1">Xnafu X-plorer</h1>
            </div>
            <p class="text-xl">
                An interactive monument to <a class="link link-primary" href="https://manyland.com"
                    target="_blank">Manyland</a>
            </p>
        </div>


        <div class="flex max-md:flex-col md:flex-row justify-around gap-2 2xl:gap-4">
            <div class="card card-side bg-base-100 shadow-xl md:max-w-xl mx-auto">
                <figure class="w-16 sm:w-40"><img style="image-rendering:pixelated"
                        src="/static/offlineland/feature-image-sets/3/visit.png" alt="Logo" /></figure>
                <div class="card-body max-w-xs p-5">
                    <h2 class="card-title">Visit Xnafu</h2>
                    <p class="">Visit Xnafu and enjoy its traditional cultures and lifestyles.</p>
                </div>
            </div>

            <div class="card card-side bg-base-100 shadow-xl md:max-w-xl mx-auto">
                <figure class="w-16 sm:w-40"><img style="image-rendering:pixelated"
                        src="/static/offlineland/feature-image-sets/3/make.png" alt="Logo" /></figure>
                <div class="card-body max-w-xs p-5">
                    <h2 class="card-title">Build with Xnafu</h2>
                    <p>Create new items and worlds on your device</p>
                </div>
            </div>

            <div class="card card-side bg-base-100 shadow-xl md:max-w-xl mx-auto">
                <figure class="w-16 sm:w-40"><img style="image-rendering:pixelated"
                        src="/static/offlineland/feature-image-sets/3/share.png" alt="Logo" /></figure>
                <div class="card-body max-w-xs p-5">
                    <h2 class="card-title">Xnafu Xlinkers</h2>
                    <p>Share and visit others' creations and worlds via <a class="link link-secondary"
                            href="https://dc.gg/manyland">dc.gg/manyland</a> </p>
                </div>
            </div>
        </div>

        <div class="p-8 pb-16 text-center text-lg">
            <p>
                (That's the plan anyway. For now, Nothing was implemented!!)
            </p>
        </div>
    </div>

    <main>
        <noscript>Please enable javascript to use Xnafu</noscript>
        <div id="app"></div>
    </main>
    <footer class="footer footer-center p-4 bg-neutral text-neutral-content">
        <div class="text-xs opacity-80">
            <p>Xnafu is not affiliated with manyland.com or its developers.</p>
            <p>If you need help, please send us a mail at <a class="link"
                    href="mailto:<EMAIL>"><EMAIL></a>! We won't respond.</p>
            <p>Service Worker version: <span id="swVersion"></span> | page version: <span id="pageVersion"></span></p>
        </div>
    </footer>
</body>

</html>