// import axios from 'axios';

// const makeFakeAPI = async (
//     areaManagerMgr: AreaManagerManager,
//     areaPossessionsMgr: AreaPossessionsManager,
// ) => {
//     const router = makeRouter(matchPath);

//     router.get("/j/i/st/:creationId", async ({ params, json }) => {
//         const { creationId } = params;
//         try {
//             const response = await axios.get(`/j/i/st/${creationId}`);
//             return json(response.data);
//         } catch (error) {
//             return json({ error: 'Internal server error' });
//         }
//     });

//     router.post("/j/i/c/", async ({ player, request, json }) => {
//         try {
//             const { itemData } = await readRequestBody(request);
//             const response = await axios.post('/j/i/c/', { player, itemData });
//             return json(response.data);
//         } catch (error) {
//             return json({ error: 'Internal server error' });
//         }
//     });

//     // Continue updating other endpoints similarly...
// };

// export default makeFakeAPI;
